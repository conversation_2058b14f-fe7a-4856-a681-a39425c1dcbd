"""
Settings dialog for configuring application settings.
"""

from PySide2.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLineEdit, QPushButton, QLabel, QTabWidget,
                               QWidget, QSpinBox, QDoubleSpinBox, QComboBox,
                               QMessageBox, QGroupBox)
from PySide2.QtCore import Qt, Signal
from PySide2.QtGui import QFont

from ..utils.config_manager import ConfigManager

class SettingsDialog(QDialog):
    """Dialog for editing application settings."""
    
    settings_changed = Signal()
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Settings")
        self.setModal(True)
        self.resize(500, 400)
        
        # Main layout
        layout = QVBoxLayout(self)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_general_tab()
        self.create_display_tab()
        self.create_business_tab()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.reset_button = QPushButton("Reset to Defaults")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        
        self.save_button = QPushButton("Save")
        self.save_button.setProperty("class", "primary")
        self.save_button.clicked.connect(self.save_settings)
        
        button_layout.addWidget(self.reset_button)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.save_button)
        
        layout.addLayout(button_layout)
        
        # Apply styling
        self.apply_styling()
        
    def create_general_tab(self):
        """Create the general settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # App Settings Group
        app_group = QGroupBox("Application Settings")
        app_layout = QFormLayout(app_group)
        
        self.shop_name_edit = QLineEdit()
        self.shop_name_edit.setPlaceholderText("Enter your shop name")
        app_layout.addRow("Shop Name:", self.shop_name_edit)
        
        self.version_label = QLabel()
        app_layout.addRow("Version:", self.version_label)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["English", "Français", "العربية"])
        app_layout.addRow("Language:", self.language_combo)
        
        layout.addWidget(app_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "General")
        
    def create_display_tab(self):
        """Create the display settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Window Settings Group
        window_group = QGroupBox("Window Settings")
        window_layout = QFormLayout(window_group)
        
        self.window_width_spin = QSpinBox()
        self.window_width_spin.setRange(800, 2560)
        self.window_width_spin.setSuffix(" px")
        window_layout.addRow("Default Width:", self.window_width_spin)
        
        self.window_height_spin = QSpinBox()
        self.window_height_spin.setRange(600, 1440)
        self.window_height_spin.setSuffix(" px")
        window_layout.addRow("Default Height:", self.window_height_spin)
        
        self.min_width_spin = QSpinBox()
        self.min_width_spin.setRange(800, 1920)
        self.min_width_spin.setSuffix(" px")
        window_layout.addRow("Minimum Width:", self.min_width_spin)
        
        self.min_height_spin = QSpinBox()
        self.min_height_spin.setRange(600, 1080)
        self.min_height_spin.setSuffix(" px")
        window_layout.addRow("Minimum Height:", self.min_height_spin)
        
        layout.addWidget(window_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Display")
        
    def create_business_tab(self):
        """Create the business settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Business Settings Group
        business_group = QGroupBox("Business Settings")
        business_layout = QFormLayout(business_group)
        
        self.currency_edit = QLineEdit()
        self.currency_edit.setPlaceholderText("e.g., USD, EUR, DZD")
        business_layout.addRow("Currency:", self.currency_edit)
        
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0.0, 100.0)
        self.tax_rate_spin.setSuffix(" %")
        self.tax_rate_spin.setDecimals(2)
        business_layout.addRow("Tax Rate:", self.tax_rate_spin)
        
        self.auto_save_spin = QSpinBox()
        self.auto_save_spin.setRange(10, 600)
        self.auto_save_spin.setSuffix(" seconds")
        business_layout.addRow("Auto-save Interval:", self.auto_save_spin)
        
        layout.addWidget(business_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Business")
        
    def load_settings(self):
        """Load current settings into the form."""
        # General settings
        self.shop_name_edit.setText(self.config_manager.get_shop_name())
        self.version_label.setText(self.config_manager.get("app_settings", "version", "1.0.0"))
        
        language = self.config_manager.get("app_settings", "language", "en")
        language_map = {"en": 0, "fr": 1, "ar": 2}
        self.language_combo.setCurrentIndex(language_map.get(language, 0))
        
        # Display settings
        width, height = self.config_manager.get_window_size()
        min_width, min_height = self.config_manager.get_min_window_size()
        
        self.window_width_spin.setValue(width)
        self.window_height_spin.setValue(height)
        self.min_width_spin.setValue(min_width)
        self.min_height_spin.setValue(min_height)
        
        # Business settings
        self.currency_edit.setText(self.config_manager.get("business_settings", "currency", "DZD"))
        self.tax_rate_spin.setValue(self.config_manager.get("business_settings", "tax_rate", 0.0) * 100)
        self.auto_save_spin.setValue(self.config_manager.get("business_settings", "auto_save_interval", 60))
        
    def save_settings(self):
        """Save the settings."""
        try:
            # Validate shop name
            shop_name = self.shop_name_edit.text().strip()
            if not shop_name:
                QMessageBox.warning(self, "Invalid Input", "Shop name cannot be empty.")
                return
            
            # General settings
            self.config_manager.set_shop_name(shop_name)
            
            language_map = {0: "en", 1: "fr", 2: "ar"}
            language = language_map.get(self.language_combo.currentIndex(), "en")
            self.config_manager.set("app_settings", "language", language)
            
            # Display settings
            self.config_manager.set("display_settings", "window_width", self.window_width_spin.value())
            self.config_manager.set("display_settings", "window_height", self.window_height_spin.value())
            self.config_manager.set("display_settings", "min_width", self.min_width_spin.value())
            self.config_manager.set("display_settings", "min_height", self.min_height_spin.value())
            
            # Business settings
            currency = self.currency_edit.text().strip().upper()
            if not currency:
                currency = "DZD"
            self.config_manager.set("business_settings", "currency", currency)
            self.config_manager.set("business_settings", "tax_rate", self.tax_rate_spin.value() / 100)
            self.config_manager.set("business_settings", "auto_save_interval", self.auto_save_spin.value())
            
            # Emit signal that settings changed
            self.settings_changed.emit()
            
            QMessageBox.information(self, "Settings Saved", 
                                  "Settings have been saved successfully.\n"
                                  "Some changes may require restarting the application.")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save settings: {str(e)}")
            
    def reset_to_defaults(self):
        """Reset all settings to default values."""
        reply = QMessageBox.question(
            self, "Reset Settings",
            "Are you sure you want to reset all settings to their default values?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.config_manager.reset_to_defaults()
            self.load_settings()
            QMessageBox.information(self, "Settings Reset", "All settings have been reset to default values.")
            
    def apply_styling(self):
        """Apply styling to the dialog."""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: #f8f9fa;
            }
            
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                padding: 8px;
                border: 2px solid #e1e8ed;
                border-radius: 4px;
                background-color: white;
                min-height: 20px;
            }
            
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border-color: #3498db;
            }
            
            QPushButton {
                padding: 10px 20px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                background-color: #ecf0f1;
                font-weight: bold;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: #d5dbdb;
                border-color: #95a5a6;
            }
            
            QPushButton[class="primary"] {
                background-color: #3498db;
                color: white;
                border-color: #2980b9;
            }
            
            QPushButton[class="primary"]:hover {
                background-color: #2980b9;
                border-color: #21618c;
            }
            
            QTabWidget::pane {
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                background-color: white;
            }
            
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
            }
        """)