{"pagination": {"DescribeFleetAttributes": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "FleetAttributes"}, "DescribeFleetCapacity": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "FleetCapacity"}, "DescribeFleetEvents": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "Events"}, "DescribeFleetUtilization": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "FleetUtilization"}, "DescribeGameSessionDetails": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "GameSessionDetails"}, "DescribeGameSessionQueues": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "GameSessionQueues"}, "DescribeGameSessions": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "GameSessions"}, "DescribeInstances": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "Instances"}, "DescribeMatchmakingConfigurations": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "Configurations"}, "DescribeMatchmakingRuleSets": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "RuleSets"}, "DescribePlayerSessions": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "PlayerSessions"}, "DescribeScalingPolicies": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "ScalingPolicies"}, "ListAliases": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "Aliases"}, "ListBuilds": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "Builds"}, "ListFleets": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "FleetIds"}, "SearchGameSessions": {"input_token": "NextToken", "limit_key": "Limit", "output_token": "NextToken", "result_key": "GameSessions"}, "DescribeGameServerInstances": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "GameServerInstances"}, "ListGameServerGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "GameServerGroups"}, "ListGameServers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "GameServers"}, "ListScripts": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "<PERSON><PERSON><PERSON>"}, "ListCompute": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "ComputeList"}, "ListLocations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "Locations"}, "ListContainerGroupDefinitions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "ContainerGroupDefinitions"}, "ListContainerFleets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "ContainerFleets"}, "ListContainerGroupDefinitionVersions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "ContainerGroupDefinitions"}, "ListFleetDeployments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "Limit", "result_key": "FleetDeployments"}}}