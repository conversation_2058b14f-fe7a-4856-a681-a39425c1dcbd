"""
Statistics panel for displaying comprehensive analytics.
"""

from PySide2.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QLabel, QScrollArea, QFrame, QGridLayout,
                               QComboBox, QTableWidget, QTableWidgetItem,
                               QHeaderView, QAbstractItemView, QTabWidget,
                               QProgressBar, QSpinBox)
from PySide2.QtCore import Qt
from PySide2.QtGui import QFont, QPalette
from datetime import datetime, timedelta
from collections import defaultdict

# Matplotlib imports
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.style as mplstyle

from ..models.data_manager import DataManager
from ..ai.ai_coordinator import AICoordinator

class StatisticsPanel(QWidget):
    """Panel for displaying comprehensive statistics and analytics."""
    
    def __init__(self, data_manager: DataManager, products_panel=None):
        super().__init__()
        self.data_manager = data_manager
        self.products_panel = products_panel

        # Initialize AI coordinator
        try:
            self.ai_coordinator = AICoordinator()
        except Exception as e:
            print(f"Warning: AI Coordinator initialization failed: {e}")
            self.ai_coordinator = None

        self.setup_ui()
        
    def setup_ui(self):
        """Set up the panel UI."""
        # Set solid background for the panel with proper text styling
        self.setStyleSheet("""
            background-color: #f8f9fa;
            color: #2c3e50;
            QLabel {
                color: #2c3e50;
                background-color: transparent;
            }
            QPushButton {
                color: white;
                background-color: #3498db;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("📊 Statistics Dashboard")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50;")
        
        # Time period selector
        period_layout = QHBoxLayout()
        period_layout.addWidget(QLabel("Period:"))
        
        self.period_spinbox = QSpinBox()
        self.period_spinbox.setRange(1, 365)
        self.period_spinbox.setValue(30)
        self.period_spinbox.setSuffix(" days")
        self.period_spinbox.valueChanged.connect(self.refresh_statistics)
        
        self.refresh_button = QPushButton("🔄 Refresh")
        self.refresh_button.setProperty("class", "info")
        self.refresh_button.clicked.connect(self.refresh_statistics)
        
        period_layout.addWidget(self.period_spinbox)
        period_layout.addWidget(self.refresh_button)
        period_layout.addStretch()
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addLayout(period_layout)
        
        layout.addLayout(header_layout)
        
        # Tab widget for different views
        self.tab_widget = QTabWidget()
        
        # Overview tab
        self.overview_tab = self.create_overview_tab()
        self.tab_widget.addTab(self.overview_tab, "📈 Overview")
        
        # Category comparison tab
        self.category_tab = self.create_category_tab()
        self.tab_widget.addTab(self.category_tab, "🏷️ By Category")
        
        # Device performance tab
        self.device_tab = self.create_device_tab()
        self.tab_widget.addTab(self.device_tab, "🎮 By Device")
        
        # Time analysis tab
        self.time_tab = self.create_time_tab()
        self.tab_widget.addTab(self.time_tab, "⏰ Time Analysis")

        # Products tab
        self.products_tab = self.create_products_tab()
        self.tab_widget.addTab(self.products_tab, "🛒 Products")

        # Recommendations tab
        self.recommendations_tab = self.create_recommendations_tab()
        self.tab_widget.addTab(self.recommendations_tab, "🤖 AI Recommendations")

        layout.addWidget(self.tab_widget)
        
        # Load initial data
        self.refresh_statistics()
        
    def create_overview_tab(self):
        """Create the overview statistics tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        
        # Key metrics cards
        metrics_layout = QGridLayout()
        
        # Create metric cards
        self.total_revenue_card = self.create_metric_card("Total Revenue", "0.00 DZD", "#27ae60")
        self.total_sessions_card = self.create_metric_card("Total Sessions", "0", "#3498db")
        self.active_devices_card = self.create_metric_card("Active Devices", "0", "#e74c3c")
        self.avg_session_card = self.create_metric_card("Avg Session", "0.00 DZD", "#f39c12")
        
        metrics_layout.addWidget(self.total_revenue_card, 0, 0)
        metrics_layout.addWidget(self.total_sessions_card, 0, 1)
        metrics_layout.addWidget(self.active_devices_card, 0, 2)
        metrics_layout.addWidget(self.avg_session_card, 0, 3)
        
        layout.addLayout(metrics_layout)
        
        # Performance summary
        summary_frame = QFrame()
        summary_frame.setFrameStyle(QFrame.StyledPanel)
        summary_layout = QVBoxLayout(summary_frame)
        
        summary_title = QLabel("Performance Summary")
        summary_title.setFont(QFont("Arial", 12, QFont.Bold))
        summary_layout.addWidget(summary_title)
        
        self.performance_table = QTableWidget()
        self.performance_table.setMaximumHeight(200)
        summary_layout.addWidget(self.performance_table)
        
        layout.addWidget(summary_frame)

        # Overview charts section
        overview_charts_frame = QFrame()
        overview_charts_frame.setFrameStyle(QFrame.StyledPanel)
        overview_charts_layout = QVBoxLayout(overview_charts_frame)

        overview_charts_title = QLabel("Revenue Overview")
        overview_charts_title.setFont(QFont("Arial", 12, QFont.Bold))
        overview_charts_layout.addWidget(overview_charts_title)

        # Charts container
        overview_charts_container = QHBoxLayout()

        # Revenue trend chart
        self.overview_revenue_chart_frame = QFrame()
        self.overview_revenue_chart_frame.setMinimumSize(400, 200)
        self.overview_revenue_chart_frame.setMaximumSize(450, 220)
        overview_revenue_layout = QVBoxLayout(self.overview_revenue_chart_frame)

        revenue_chart_title = QLabel("Revenue Trend")
        revenue_chart_title.setFont(QFont("Arial", 10, QFont.Bold))
        revenue_chart_title.setAlignment(Qt.AlignCenter)
        overview_revenue_layout.addWidget(revenue_chart_title)

        # Sessions chart
        self.overview_sessions_chart_frame = QFrame()
        self.overview_sessions_chart_frame.setMinimumSize(300, 200)
        self.overview_sessions_chart_frame.setMaximumSize(350, 220)
        overview_sessions_layout = QVBoxLayout(self.overview_sessions_chart_frame)

        sessions_chart_title = QLabel("Sessions by Type")
        sessions_chart_title.setFont(QFont("Arial", 10, QFont.Bold))
        sessions_chart_title.setAlignment(Qt.AlignCenter)
        overview_sessions_layout.addWidget(sessions_chart_title)

        overview_charts_container.addWidget(self.overview_revenue_chart_frame)
        overview_charts_container.addWidget(self.overview_sessions_chart_frame)

        overview_charts_layout.addLayout(overview_charts_container)
        layout.addWidget(overview_charts_frame)

        return tab
        
    def create_category_tab(self):
        """Create the category comparison tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Category comparison table
        self.category_table = QTableWidget()
        self.category_table.setAlternatingRowColors(True)
        layout.addWidget(self.category_table)

        # Category charts section
        category_charts_frame = QFrame()
        category_charts_frame.setFrameStyle(QFrame.StyledPanel)
        category_charts_layout = QVBoxLayout(category_charts_frame)

        category_charts_title = QLabel("Category Performance Charts")
        category_charts_title.setFont(QFont("Arial", 12, QFont.Bold))
        category_charts_layout.addWidget(category_charts_title)

        # Category bar chart
        self.category_bar_chart_frame = QFrame()
        self.category_bar_chart_frame.setMinimumSize(600, 250)
        category_bar_layout = QVBoxLayout(self.category_bar_chart_frame)

        category_bar_title = QLabel("Sessions and Revenue by Category")
        category_bar_title.setFont(QFont("Arial", 10, QFont.Bold))
        category_bar_title.setAlignment(Qt.AlignCenter)
        category_bar_layout.addWidget(category_bar_title)

        category_charts_layout.addWidget(self.category_bar_chart_frame)
        layout.addWidget(category_charts_frame)

        return tab
        
    def create_device_tab(self):
        """Create the device performance tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Device performance table
        self.device_table = QTableWidget()
        self.device_table.setAlternatingRowColors(True)
        self.device_table.setSortingEnabled(True)
        layout.addWidget(self.device_table)

        # Device charts section
        device_charts_frame = QFrame()
        device_charts_frame.setFrameStyle(QFrame.StyledPanel)
        device_charts_layout = QVBoxLayout(device_charts_frame)

        device_charts_title = QLabel("Device Performance Charts")
        device_charts_title.setFont(QFont("Arial", 12, QFont.Bold))
        device_charts_layout.addWidget(device_charts_title)

        # Device charts container
        device_charts_container = QHBoxLayout()

        # Device usage pie chart
        self.device_pie_chart_frame = QFrame()
        self.device_pie_chart_frame.setMinimumSize(300, 200)
        self.device_pie_chart_frame.setMaximumSize(350, 220)
        device_pie_layout = QVBoxLayout(self.device_pie_chart_frame)

        device_pie_title = QLabel("Device Usage Distribution")
        device_pie_title.setFont(QFont("Arial", 10, QFont.Bold))
        device_pie_title.setAlignment(Qt.AlignCenter)
        device_pie_layout.addWidget(device_pie_title)

        # Device revenue bar chart
        self.device_revenue_chart_frame = QFrame()
        self.device_revenue_chart_frame.setMinimumSize(350, 200)
        self.device_revenue_chart_frame.setMaximumSize(400, 220)
        device_revenue_layout = QVBoxLayout(self.device_revenue_chart_frame)

        device_revenue_title = QLabel("Revenue by Device")
        device_revenue_title.setFont(QFont("Arial", 10, QFont.Bold))
        device_revenue_title.setAlignment(Qt.AlignCenter)
        device_revenue_layout.addWidget(device_revenue_title)

        device_charts_container.addWidget(self.device_pie_chart_frame)
        device_charts_container.addWidget(self.device_revenue_chart_frame)

        device_charts_layout.addLayout(device_charts_container)
        layout.addWidget(device_charts_frame)

        return tab
        
    def create_time_tab(self):
        """Create the time analysis tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Daily revenue chart (simplified as table)
        daily_frame = QFrame()
        daily_frame.setFrameStyle(QFrame.StyledPanel)
        daily_layout = QVBoxLayout(daily_frame)
        
        daily_title = QLabel("Daily Revenue (Last 7 Days)")
        daily_title.setFont(QFont("Arial", 12, QFont.Bold))
        daily_layout.addWidget(daily_title)
        
        self.daily_table = QTableWidget()
        self.daily_table.setMaximumHeight(200)
        daily_layout.addWidget(self.daily_table)
        
        layout.addWidget(daily_frame)
        
        # Hourly usage chart
        hourly_frame = QFrame()
        hourly_frame.setFrameStyle(QFrame.StyledPanel)
        hourly_layout = QVBoxLayout(hourly_frame)
        
        hourly_title = QLabel("Hourly Usage Distribution")
        hourly_title.setFont(QFont("Arial", 12, QFont.Bold))
        hourly_layout.addWidget(hourly_title)
        
        self.hourly_table = QTableWidget()
        hourly_layout.addWidget(self.hourly_table)
        
        layout.addWidget(hourly_frame)

        # Time analysis charts section
        time_charts_frame = QFrame()
        time_charts_frame.setFrameStyle(QFrame.StyledPanel)
        time_charts_layout = QVBoxLayout(time_charts_frame)

        time_charts_title = QLabel("Time Analysis Charts")
        time_charts_title.setFont(QFont("Arial", 12, QFont.Bold))
        time_charts_layout.addWidget(time_charts_title)

        # Time charts container
        time_charts_container = QHBoxLayout()

        # Hourly usage bar chart
        self.hourly_chart_frame = QFrame()
        self.hourly_chart_frame.setMinimumSize(400, 200)
        self.hourly_chart_frame.setMaximumSize(450, 220)
        hourly_chart_layout = QVBoxLayout(self.hourly_chart_frame)

        hourly_chart_title = QLabel("Hourly Usage Pattern")
        hourly_chart_title.setFont(QFont("Arial", 10, QFont.Bold))
        hourly_chart_title.setAlignment(Qt.AlignCenter)
        hourly_chart_layout.addWidget(hourly_chart_title)

        # Weekly trend chart
        self.weekly_chart_frame = QFrame()
        self.weekly_chart_frame.setMinimumSize(300, 200)
        self.weekly_chart_frame.setMaximumSize(350, 220)
        weekly_chart_layout = QVBoxLayout(self.weekly_chart_frame)

        weekly_chart_title = QLabel("Weekly Revenue Trend")
        weekly_chart_title.setFont(QFont("Arial", 10, QFont.Bold))
        weekly_chart_title.setAlignment(Qt.AlignCenter)
        weekly_chart_layout.addWidget(weekly_chart_title)

        time_charts_container.addWidget(self.hourly_chart_frame)
        time_charts_container.addWidget(self.weekly_chart_frame)

        time_charts_layout.addLayout(time_charts_container)
        layout.addWidget(time_charts_frame)

        return tab

    def create_recommendations_tab(self):
        """Create the AI recommendations tab."""
        tab = QWidget()
        main_layout = QVBoxLayout(tab)

        # Create scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create content widget
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(15)

        # AI Header
        ai_header = QFrame()
        ai_header.setFrameStyle(QFrame.StyledPanel)
        ai_header_layout = QVBoxLayout(ai_header)

        ai_title = QLabel("🤖 Advanced AI Business Intelligence")
        ai_title.setFont(QFont("Arial", 16, QFont.Bold))
        ai_title.setAlignment(Qt.AlignCenter)
        ai_title.setStyleSheet("color: #2c3e50; padding: 10px;")
        ai_header_layout.addWidget(ai_title)

        ai_subtitle = QLabel("PuLP Optimization • Experta Rules • PyOD Anomaly Detection • Predictive Analytics")
        ai_subtitle.setFont(QFont("Arial", 10))
        ai_subtitle.setAlignment(Qt.AlignCenter)
        ai_subtitle.setStyleSheet("color: #7f8c8d; font-style: italic;")
        ai_header_layout.addWidget(ai_subtitle)


        layout.addWidget(ai_header)

        # Executive Summary Section
        executive_frame = QFrame()
        executive_frame.setFrameStyle(QFrame.StyledPanel)
        executive_layout = QVBoxLayout(executive_frame)

        executive_title = QLabel("📊 Executive Summary")
        executive_title.setFont(QFont("Arial", 14, QFont.Bold))
        executive_layout.addWidget(executive_title)

        self.executive_summary_text = QLabel()
        self.executive_summary_text.setWordWrap(True)
        self.executive_summary_text.setStyleSheet("padding: 10px; background-color: #e8f4fd; border-radius: 5px; font-weight: bold;")
        executive_layout.addWidget(self.executive_summary_text)

        layout.addWidget(executive_frame)

        # Smart Pricing Section
        pricing_frame = QFrame()
        pricing_frame.setFrameStyle(QFrame.StyledPanel)
        pricing_layout = QVBoxLayout(pricing_frame)

        pricing_title = QLabel("� Smart Pricing Optimization (PuLP)")
        pricing_title.setFont(QFont("Arial", 14, QFont.Bold))
        pricing_layout.addWidget(pricing_title)

        self.pricing_text = QLabel()
        self.pricing_text.setWordWrap(True)
        self.pricing_text.setStyleSheet("padding: 10px; background-color: #f0f8f0; border-radius: 5px;")
        pricing_layout.addWidget(self.pricing_text)

        layout.addWidget(pricing_frame)

        # Promotions & Rules Section
        promotions_frame = QFrame()
        promotions_frame.setFrameStyle(QFrame.StyledPanel)
        promotions_layout = QVBoxLayout(promotions_frame)

        promotions_title = QLabel("🎯 Smart Promotions (Experta Rules)")
        promotions_title.setFont(QFont("Arial", 14, QFont.Bold))
        promotions_layout.addWidget(promotions_title)

        self.promotions_text = QLabel()
        self.promotions_text.setWordWrap(True)
        self.promotions_text.setStyleSheet("padding: 10px; background-color: #fff8e1; border-radius: 5px;")
        promotions_layout.addWidget(self.promotions_text)

        layout.addWidget(promotions_frame)

        # Anomaly Detection Section
        anomaly_frame = QFrame()
        anomaly_frame.setFrameStyle(QFrame.StyledPanel)
        anomaly_layout = QVBoxLayout(anomaly_frame)

        anomaly_title = QLabel("⚠️ Anomaly Detection (PyOD)")
        anomaly_title.setFont(QFont("Arial", 14, QFont.Bold))
        anomaly_layout.addWidget(anomaly_title)

        self.anomaly_text = QLabel()
        self.anomaly_text.setWordWrap(True)
        self.anomaly_text.setStyleSheet("padding: 10px; background-color: #ffeaa7; border-radius: 5px;")
        anomaly_layout.addWidget(self.anomaly_text)

        layout.addWidget(anomaly_frame)

        # Action Items Section
        action_frame = QFrame()
        action_frame.setFrameStyle(QFrame.StyledPanel)
        action_layout = QVBoxLayout(action_frame)

        action_title = QLabel("📋 Priority Action Items")
        action_title.setFont(QFont("Arial", 14, QFont.Bold))
        action_layout.addWidget(action_title)

        self.action_items_text = QLabel()
        self.action_items_text.setWordWrap(True)
        self.action_items_text.setStyleSheet("padding: 10px; background-color: #ffebee; border-radius: 5px;")
        action_layout.addWidget(self.action_items_text)

        layout.addWidget(action_frame)

        # Set up scroll area
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

        return tab

    def create_metric_card(self, title: str, value: str, color: str):
        """Create a metric card widget."""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                margin: 5px;
            }}
        """)
        card.setMinimumHeight(80)
        card.setMaximumHeight(100)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 12px;")

        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("color: #2c3e50; font-weight: bold; font-size: 16px;")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        # Store reference to value label for updates
        card.value_label = value_label

        return card

    def update_metric_card(self, card, new_value: str):
        """Update the value of a metric card."""
        if hasattr(card, 'value_label'):
            card.value_label.setText(new_value)
        
    def refresh_statistics(self):
        """Refresh all statistics data."""
        days = self.period_spinbox.value()
        stats = self.data_manager.get_statistics(days)
        
        self.update_overview(stats)
        self.update_category_comparison(stats)
        self.update_device_performance(stats)
        self.update_time_analysis(stats)
        self.update_products_statistics()

        # Update all charts
        self.update_overview_charts(stats)
        self.update_category_charts(stats)
        self.update_device_charts(stats)
        self.update_time_charts(stats)

        # Update AI recommendations
        self.update_ai_recommendations(stats)
        
    def update_overview(self, stats):
        """Update the overview tab."""
        # Update metric cards
        self.total_revenue_card.value_label.setText(f"{stats['total_revenue']:.2f} DZD")
        self.total_sessions_card.value_label.setText(str(stats['total_sessions']))
        
        # Count active devices
        active_devices = len([d for d in self.data_manager.get_all_devices() if d.current_session])
        self.active_devices_card.value_label.setText(str(active_devices))
        
        # Calculate average session value
        avg_session = stats['total_revenue'] / max(stats['total_sessions'], 1)
        self.avg_session_card.value_label.setText(f"{avg_session:.2f} DZD")
        
        # Update performance summary
        self.performance_table.setRowCount(4)
        self.performance_table.setColumnCount(2)
        self.performance_table.setHorizontalHeaderLabels(["Metric", "Value"])
        
        total_time_hours = stats['total_active_time'] / 3600
        
        metrics = [
            ("Total Active Time", f"{total_time_hours:.1f} hours"),
            ("Average Session Duration", f"{total_time_hours / max(stats['total_sessions'], 1):.1f} hours"),
            ("Revenue per Hour", f"{stats['total_revenue'] / max(total_time_hours, 1):.2f} DZD"),
            ("Most Popular Category", self.get_most_popular_category(stats))
        ]
        
        for row, (metric, value) in enumerate(metrics):
            self.performance_table.setItem(row, 0, QTableWidgetItem(metric))
            self.performance_table.setItem(row, 1, QTableWidgetItem(str(value)))
            
        self.performance_table.resizeColumnsToContents()
        
    def update_category_comparison(self, stats):
        """Update the category comparison tab."""
        categories = stats['revenue_by_type']
        
        self.category_table.setRowCount(len(categories))
        self.category_table.setColumnCount(5)
        self.category_table.setHorizontalHeaderLabels([
            "Category", "Revenue (DZD)", "Sessions", "Avg Revenue/Session", "Market Share %"
        ])
        
        total_revenue = stats['total_revenue']
        
        for row, (category, revenue) in enumerate(categories.items()):
            sessions = stats['sessions_by_type'].get(category, 0)
            avg_revenue = revenue / max(sessions, 1)
            market_share = (revenue / max(total_revenue, 1)) * 100
            
            self.category_table.setItem(row, 0, QTableWidgetItem(category.replace('_', ' ').title()))
            self.category_table.setItem(row, 1, QTableWidgetItem(f"{revenue:.2f}"))
            self.category_table.setItem(row, 2, QTableWidgetItem(str(sessions)))
            self.category_table.setItem(row, 3, QTableWidgetItem(f"{avg_revenue:.2f}"))
            self.category_table.setItem(row, 4, QTableWidgetItem(f"{market_share:.1f}%"))
            
        self.category_table.resizeColumnsToContents()
        
    def update_device_performance(self, stats):
        """Update the device performance tab."""
        devices = stats['revenue_by_device']
        
        self.device_table.setRowCount(len(devices))
        self.device_table.setColumnCount(4)
        self.device_table.setHorizontalHeaderLabels([
            "Device", "Revenue (DZD)", "Sessions", "Avg Revenue/Session"
        ])
        
        for row, (device_name, revenue) in enumerate(devices.items()):
            sessions = stats['sessions_by_device'].get(device_name, 0)
            avg_revenue = revenue / max(sessions, 1)
            
            self.device_table.setItem(row, 0, QTableWidgetItem(device_name))
            
            revenue_item = QTableWidgetItem(f"{revenue:.2f}")
            revenue_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.device_table.setItem(row, 1, revenue_item)
            
            sessions_item = QTableWidgetItem(str(sessions))
            sessions_item.setTextAlignment(Qt.AlignCenter)
            self.device_table.setItem(row, 2, sessions_item)
            
            avg_item = QTableWidgetItem(f"{avg_revenue:.2f}")
            avg_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.device_table.setItem(row, 3, avg_item)
            
        self.device_table.resizeColumnsToContents()
        
    def update_time_analysis(self, stats):
        """Update the time analysis tab."""
        # Daily revenue (last 7 days)
        daily_revenue = stats['daily_revenue']
        recent_days = sorted(daily_revenue.keys())[-7:]  # Last 7 days
        
        self.daily_table.setRowCount(len(recent_days))
        self.daily_table.setColumnCount(2)
        self.daily_table.setHorizontalHeaderLabels(["Date", "Revenue (DZD)"])
        
        for row, date in enumerate(recent_days):
            revenue = daily_revenue[date]
            self.daily_table.setItem(row, 0, QTableWidgetItem(date))
            
            revenue_item = QTableWidgetItem(f"{revenue:.2f}")
            revenue_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.daily_table.setItem(row, 1, revenue_item)
            
        self.daily_table.resizeColumnsToContents()
        
        # Hourly usage
        hourly_usage = stats['hourly_usage']
        
        self.hourly_table.setRowCount(24)
        self.hourly_table.setColumnCount(3)
        self.hourly_table.setHorizontalHeaderLabels(["Hour", "Sessions", "Usage %"])
        
        total_sessions = sum(hourly_usage.values()) or 1
        
        for hour in range(24):
            sessions = hourly_usage.get(hour, 0)
            percentage = (sessions / total_sessions) * 100
            
            self.hourly_table.setItem(hour, 0, QTableWidgetItem(f"{hour:02d}:00"))
            
            sessions_item = QTableWidgetItem(str(sessions))
            sessions_item.setTextAlignment(Qt.AlignCenter)
            self.hourly_table.setItem(hour, 1, sessions_item)
            
            percentage_item = QTableWidgetItem(f"{percentage:.1f}%")
            percentage_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.hourly_table.setItem(hour, 2, percentage_item)
            
        self.hourly_table.resizeColumnsToContents()
        
    def get_most_popular_category(self, stats):
        """Get the most popular category by sessions."""
        sessions_by_type = stats['sessions_by_type']
        if not sessions_by_type:
            return "None"

        most_popular = max(sessions_by_type.items(), key=lambda x: x[1])
        return most_popular[0].replace('_', ' ').title()

    def create_products_tab(self):
        """Create the products statistics tab."""
        tab = QWidget()
        main_layout = QVBoxLayout(tab)

        # Create scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create content widget
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        layout.setSpacing(15)

        # Products overview section
        overview_frame = QFrame()
        overview_layout = QVBoxLayout(overview_frame)

        overview_title = QLabel("Products Overview")
        overview_title.setFont(QFont("Arial", 14, QFont.Bold))
        overview_layout.addWidget(overview_title)

        # Products metrics - organized in sections
        products_metrics_layout = QGridLayout()
        products_metrics_layout.setSpacing(10)

        # Create product metric cards - Row 1: Product Counts
        self.total_products_card = self.create_metric_card("Total Products", "0", "#9b59b6")
        self.drinks_count_card = self.create_metric_card("🥤 Drinks", "0", "#3498db")
        self.chips_count_card = self.create_metric_card("🍟 Chips", "0", "#f39c12")
        self.snacks_count_card = self.create_metric_card("🍫 Snacks", "0", "#e74c3c")

        # Row 2: Revenue metrics
        self.products_total_revenue_card = self.create_metric_card("Total Revenue", "0.00 DZD", "#27ae60")
        self.daily_revenue_card = self.create_metric_card("Today's Revenue", "0.00 DZD", "#2ecc71")
        self.monthly_revenue_card = self.create_metric_card("This Month", "0.00 DZD", "#16a085")
        self.total_sales_card = self.create_metric_card("Total Sales", "0", "#34495e")

        # Add cards to grid with better spacing
        products_metrics_layout.addWidget(self.total_products_card, 0, 0)
        products_metrics_layout.addWidget(self.drinks_count_card, 0, 1)
        products_metrics_layout.addWidget(self.chips_count_card, 0, 2)
        products_metrics_layout.addWidget(self.snacks_count_card, 0, 3)

        products_metrics_layout.addWidget(self.products_total_revenue_card, 1, 0)
        products_metrics_layout.addWidget(self.daily_revenue_card, 1, 1)
        products_metrics_layout.addWidget(self.monthly_revenue_card, 1, 2)
        products_metrics_layout.addWidget(self.total_sales_card, 1, 3)

        # Set column stretch to make cards equal width
        for col in range(4):
            products_metrics_layout.setColumnStretch(col, 1)

        overview_layout.addLayout(products_metrics_layout)
        layout.addWidget(overview_frame)

        # Products table
        products_frame = QFrame()
        products_frame.setFrameStyle(QFrame.StyledPanel)
        products_layout = QVBoxLayout(products_frame)

        products_title = QLabel("Products Inventory")
        products_title.setFont(QFont("Arial", 12, QFont.Bold))
        products_layout.addWidget(products_title)

        self.products_table = QTableWidget()
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        products_layout.addWidget(self.products_table)

        layout.addWidget(products_frame)

        # Revenue breakdown section
        revenue_frame = QFrame()
        revenue_frame.setFrameStyle(QFrame.StyledPanel)
        revenue_layout = QVBoxLayout(revenue_frame)

        revenue_title = QLabel("Revenue Analytics")
        revenue_title.setFont(QFont("Arial", 12, QFont.Bold))
        revenue_layout.addWidget(revenue_title)

        # Revenue tables layout
        revenue_tables_layout = QHBoxLayout()

        # Category revenue table
        category_revenue_frame = QFrame()
        category_revenue_frame.setFrameStyle(QFrame.StyledPanel)
        category_layout = QVBoxLayout(category_revenue_frame)

        category_title = QLabel("Revenue by Category")
        category_title.setFont(QFont("Arial", 10, QFont.Bold))
        category_layout.addWidget(category_title)

        self.category_revenue_table = QTableWidget()
        self.category_revenue_table.setAlternatingRowColors(True)
        self.category_revenue_table.setMaximumHeight(150)
        category_layout.addWidget(self.category_revenue_table)

        # Daily revenue table
        daily_revenue_frame = QFrame()
        daily_revenue_frame.setFrameStyle(QFrame.StyledPanel)
        daily_layout = QVBoxLayout(daily_revenue_frame)

        daily_title = QLabel("Recent Daily Revenue")
        daily_title.setFont(QFont("Arial", 10, QFont.Bold))
        daily_layout.addWidget(daily_title)

        self.daily_revenue_table = QTableWidget()
        self.daily_revenue_table.setAlternatingRowColors(True)
        self.daily_revenue_table.setMaximumHeight(150)
        daily_layout.addWidget(self.daily_revenue_table)

        revenue_tables_layout.addWidget(category_revenue_frame)
        revenue_tables_layout.addWidget(daily_revenue_frame)

        revenue_layout.addLayout(revenue_tables_layout)
        layout.addWidget(revenue_frame)

        # Charts section
        charts_frame = QFrame()
        charts_frame.setFrameStyle(QFrame.StyledPanel)
        charts_layout = QVBoxLayout(charts_frame)

        charts_title = QLabel("Revenue Charts")
        charts_title.setFont(QFont("Arial", 12, QFont.Bold))
        charts_layout.addWidget(charts_title)

        # Charts container
        charts_container = QHBoxLayout()

        # Category pie chart
        self.category_chart_frame = QFrame()
        self.category_chart_frame.setMinimumSize(300, 200)
        self.category_chart_frame.setMaximumSize(350, 220)
        category_chart_layout = QVBoxLayout(self.category_chart_frame)

        category_chart_title = QLabel("Revenue by Category")
        category_chart_title.setFont(QFont("Arial", 10, QFont.Bold))
        category_chart_title.setAlignment(Qt.AlignCenter)
        category_chart_layout.addWidget(category_chart_title)

        # Daily revenue line chart
        self.daily_chart_frame = QFrame()
        self.daily_chart_frame.setMinimumSize(350, 200)
        self.daily_chart_frame.setMaximumSize(400, 220)
        daily_chart_layout = QVBoxLayout(self.daily_chart_frame)

        daily_chart_title = QLabel("Daily Revenue Trend")
        daily_chart_title.setFont(QFont("Arial", 10, QFont.Bold))
        daily_chart_title.setAlignment(Qt.AlignCenter)
        daily_chart_layout.addWidget(daily_chart_title)

        charts_container.addWidget(self.category_chart_frame)
        charts_container.addWidget(self.daily_chart_frame)

        charts_layout.addLayout(charts_container)
        layout.addWidget(charts_frame)

        # Set up scroll area
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

        return tab

    def update_products_statistics(self):
        """Update products statistics with real data."""
        if not self.products_panel:
            # Fallback if no products panel
            self.update_metric_card(self.total_products_card, "0")
            self.update_metric_card(self.drinks_count_card, "0")
            self.update_metric_card(self.chips_count_card, "0")
            self.update_metric_card(self.snacks_count_card, "0")
            return

        # Get real products data
        products_data = self.products_panel.get_all_products_data()

        # Calculate revenue analytics
        revenue_analytics = self.calculate_revenue_analytics(products_data)

        # Update metric cards - Products
        self.update_metric_card(self.total_products_card, str(products_data['total_products']))
        self.update_metric_card(self.drinks_count_card, str(products_data['drinks_count']))
        self.update_metric_card(self.chips_count_card, str(products_data['chips_count']))
        self.update_metric_card(self.snacks_count_card, str(products_data['snacks_count']))

        # Update metric cards - Revenue
        self.update_metric_card(self.products_total_revenue_card, f"{revenue_analytics['total_revenue']:.2f} DZD")
        self.update_metric_card(self.daily_revenue_card, f"{revenue_analytics['daily_revenue']:.2f} DZD")
        self.update_metric_card(self.monthly_revenue_card, f"{revenue_analytics['monthly_revenue']:.2f} DZD")
        self.update_metric_card(self.total_sales_card, str(revenue_analytics['total_sales']))

        # Update products table
        all_products = products_data['all_products']
        self.products_table.setRowCount(len(all_products))
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels(["Product", "Category", "Price (DZD)", "Brand", "Sales"])

        if all_products:
            for row, product in enumerate(all_products):
                # Count sales for this product
                sales_count = len([sale for sale in products_data['all_sales']
                                 if sale['product_name'] == product['name']])

                self.products_table.setItem(row, 0, QTableWidgetItem(product['name']))
                self.products_table.setItem(row, 1, QTableWidgetItem(product['category'].title()))
                self.products_table.setItem(row, 2, QTableWidgetItem(f"{product['price']:.2f}"))
                self.products_table.setItem(row, 3, QTableWidgetItem(product.get('brand', 'N/A')))
                self.products_table.setItem(row, 4, QTableWidgetItem(str(sales_count)))
        else:
            # Show empty state
            self.products_table.setRowCount(1)
            self.products_table.setItem(0, 0, QTableWidgetItem("No products added yet"))
            self.products_table.setItem(0, 1, QTableWidgetItem("Go to Products tab"))
            self.products_table.setItem(0, 2, QTableWidgetItem("--"))
            self.products_table.setItem(0, 3, QTableWidgetItem("--"))
            self.products_table.setItem(0, 4, QTableWidgetItem("--"))

        self.products_table.resizeColumnsToContents()

        # Update revenue tables
        self.update_category_revenue_table(revenue_analytics['category_revenue'])
        self.update_daily_revenue_table(revenue_analytics['daily_revenue_breakdown'])

        # Update charts
        self.update_category_pie_chart(revenue_analytics['category_revenue'])
        self.update_daily_revenue_chart(revenue_analytics['daily_revenue_breakdown'])

    def calculate_revenue_analytics(self, products_data):
        """Calculate comprehensive revenue analytics."""
        from datetime import datetime, timedelta
        from collections import defaultdict

        all_sales = products_data['all_sales']
        now = datetime.now()
        today = now.date()
        current_month = now.month
        current_year = now.year

        # Initialize analytics
        analytics = {
            'total_revenue': 0.0,
            'daily_revenue': 0.0,
            'monthly_revenue': 0.0,
            'total_sales': len(all_sales),
            'category_revenue': defaultdict(lambda: {'sales': 0, 'revenue': 0.0}),
            'daily_revenue_breakdown': defaultdict(lambda: {'sales': 0, 'revenue': 0.0})
        }

        # Process each sale
        for sale in all_sales:
            sale_date = sale['timestamp'].date()
            sale_month = sale['timestamp'].month
            sale_year = sale['timestamp'].year
            price = sale['price']
            category = sale['category']

            # Total revenue
            analytics['total_revenue'] += price

            # Daily revenue (today only)
            if sale_date == today:
                analytics['daily_revenue'] += price

            # Monthly revenue (current month)
            if sale_month == current_month and sale_year == current_year:
                analytics['monthly_revenue'] += price

            # Category revenue
            analytics['category_revenue'][category]['sales'] += 1
            analytics['category_revenue'][category]['revenue'] += price

            # Daily breakdown (last 7 days)
            days_ago = (today - sale_date).days
            if days_ago <= 7:
                date_str = sale_date.strftime('%Y-%m-%d')
                analytics['daily_revenue_breakdown'][date_str]['sales'] += 1
                analytics['daily_revenue_breakdown'][date_str]['revenue'] += price

        return analytics

    def update_category_revenue_table(self, category_revenue):
        """Update the category revenue table."""
        categories = list(category_revenue.keys())
        self.category_revenue_table.setRowCount(len(categories))
        self.category_revenue_table.setColumnCount(3)
        self.category_revenue_table.setHorizontalHeaderLabels(["Category", "Sales", "Revenue (DZD)"])

        for row, category in enumerate(categories):
            data = category_revenue[category]
            self.category_revenue_table.setItem(row, 0, QTableWidgetItem(category.title()))
            self.category_revenue_table.setItem(row, 1, QTableWidgetItem(str(data['sales'])))
            self.category_revenue_table.setItem(row, 2, QTableWidgetItem(f"{data['revenue']:.2f}"))

        self.category_revenue_table.resizeColumnsToContents()

    def update_daily_revenue_table(self, daily_revenue):
        """Update the daily revenue table."""
        # Sort by date (most recent first)
        sorted_dates = sorted(daily_revenue.keys(), reverse=True)

        self.daily_revenue_table.setRowCount(len(sorted_dates))
        self.daily_revenue_table.setColumnCount(3)
        self.daily_revenue_table.setHorizontalHeaderLabels(["Date", "Sales", "Revenue (DZD)"])

        for row, date_str in enumerate(sorted_dates):
            data = daily_revenue[date_str]
            # Format date nicely
            from datetime import datetime
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%b %d, %Y')

            self.daily_revenue_table.setItem(row, 0, QTableWidgetItem(formatted_date))
            self.daily_revenue_table.setItem(row, 1, QTableWidgetItem(str(data['sales'])))
            self.daily_revenue_table.setItem(row, 2, QTableWidgetItem(f"{data['revenue']:.2f}"))

        self.daily_revenue_table.resizeColumnsToContents()

    def update_category_pie_chart(self, category_revenue):
        """Update the category revenue pie chart."""
        # Clear previous chart
        for i in reversed(range(self.category_chart_frame.layout().count())):
            child = self.category_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        if not category_revenue:
            return

        # Create matplotlib figure
        fig = Figure(figsize=(3.5, 2.5), dpi=80)
        canvas = FigureCanvas(fig)

        # Create pie chart
        ax = fig.add_subplot(111)

        categories = list(category_revenue.keys())
        revenues = [category_revenue[cat]['revenue'] for cat in categories]

        if sum(revenues) > 0:
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
            wedges, texts, autotexts = ax.pie(revenues, labels=categories, autopct='%1.1f%%',
                                            colors=colors[:len(categories)], startangle=90)

            # Style the text
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(9)
        else:
            ax.text(0.5, 0.5, 'No sales data', ha='center', va='center', transform=ax.transAxes)

        ax.set_title('Revenue by Category', fontsize=12, fontweight='bold', pad=20)
        fig.tight_layout()

        # Add to layout
        self.category_chart_frame.layout().addWidget(canvas)

    def update_daily_revenue_chart(self, daily_revenue):
        """Update the daily revenue line chart."""
        # Clear previous chart
        for i in reversed(range(self.daily_chart_frame.layout().count())):
            child = self.daily_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        # Create matplotlib figure
        fig = Figure(figsize=(4, 2.5), dpi=80)
        canvas = FigureCanvas(fig)

        # Create line chart
        ax = fig.add_subplot(111)

        if daily_revenue:
            # Sort dates and get last 7 days
            sorted_dates = sorted(daily_revenue.keys())[-7:]
            dates = []
            revenues = []

            for date_str in sorted_dates:
                from datetime import datetime
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                dates.append(date_obj.strftime('%m/%d'))
                revenues.append(daily_revenue[date_str]['revenue'])

            if dates and revenues:
                ax.plot(dates, revenues, marker='o', linewidth=2, markersize=6,
                       color='#4ECDC4', markerfacecolor='#FF6B6B')
                ax.fill_between(dates, revenues, alpha=0.3, color='#4ECDC4')

                # Style the chart
                ax.set_xlabel('Date', fontweight='bold')
                ax.set_ylabel('Revenue (DZD)', fontweight='bold')
                ax.grid(True, alpha=0.3)

                # Rotate x-axis labels
                plt.setp(ax.get_xticklabels(), rotation=45)
            else:
                ax.text(0.5, 0.5, 'No sales data', ha='center', va='center', transform=ax.transAxes)
        else:
            ax.text(0.5, 0.5, 'No sales data', ha='center', va='center', transform=ax.transAxes)

        ax.set_title('Daily Revenue Trend (Last 7 Days)', fontsize=12, fontweight='bold', pad=20)
        fig.tight_layout()

        # Add to layout
        self.daily_chart_frame.layout().addWidget(canvas)

    def update_overview_charts(self, stats):
        """Update overview tab charts."""
        self.update_overview_revenue_chart(stats)
        self.update_overview_sessions_chart(stats)

    def update_overview_revenue_chart(self, stats):
        """Update overview revenue trend chart."""
        # Clear previous chart
        for i in reversed(range(self.overview_revenue_chart_frame.layout().count())):
            child = self.overview_revenue_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        # Create matplotlib figure
        fig = Figure(figsize=(4, 2.5), dpi=80)
        canvas = FigureCanvas(fig)

        # Create line chart
        ax = fig.add_subplot(111)

        # Sample data - you can replace with real revenue trend data
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        revenue = [stats.get('total_revenue', 0) * 0.1 * (i+1) for i in range(7)]

        ax.plot(days, revenue, marker='o', linewidth=2, color='#27ae60')
        ax.fill_between(days, revenue, alpha=0.3, color='#27ae60')
        ax.set_title('Revenue Trend', fontweight='bold')
        ax.set_ylabel('Revenue (DZD)')
        ax.grid(True, alpha=0.3)

        fig.tight_layout()
        self.overview_revenue_chart_frame.layout().addWidget(canvas)

    def update_overview_sessions_chart(self, stats):
        """Update overview sessions pie chart."""
        # Clear previous chart
        for i in reversed(range(self.overview_sessions_chart_frame.layout().count())):
            child = self.overview_sessions_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        # Create matplotlib figure
        fig = Figure(figsize=(3.5, 2.5), dpi=80)
        canvas = FigureCanvas(fig)

        # Create pie chart
        ax = fig.add_subplot(111)

        sessions_by_type = stats.get('sessions_by_type', {})
        if sessions_by_type and any(v > 0 for v in sessions_by_type.values()):
            labels = []
            sizes = []
            for device_type, count in sessions_by_type.items():
                if count > 0:  # Only include devices with sessions
                    labels.append(device_type)
                    sizes.append(count)

            if sizes:  # Double check we have valid data
                colors = ['#3498db', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c']
                ax.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors[:len(labels)])
            else:
                ax.text(0.5, 0.5, 'No session data', ha='center', va='center', transform=ax.transAxes)
        else:
            ax.text(0.5, 0.5, 'No session data', ha='center', va='center', transform=ax.transAxes)

        ax.set_title('Sessions by Type', fontweight='bold')
        fig.tight_layout()
        self.overview_sessions_chart_frame.layout().addWidget(canvas)

    def update_category_charts(self, stats):
        """Update category tab charts."""
        self.update_category_bar_chart(stats)

    def update_category_bar_chart(self, stats):
        """Update category comparison bar chart."""
        # Clear previous chart
        for i in reversed(range(self.category_bar_chart_frame.layout().count())):
            child = self.category_bar_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        # Create matplotlib figure
        fig = Figure(figsize=(6, 3), dpi=80)
        canvas = FigureCanvas(fig)

        # Create bar chart
        ax = fig.add_subplot(111)

        sessions_by_type = stats.get('sessions_by_type', {})
        revenue_by_type = stats.get('revenue_by_type', {})

        if sessions_by_type:
            categories = list(sessions_by_type.keys())
            sessions = list(sessions_by_type.values())
            revenues = [revenue_by_type.get(cat, 0) for cat in categories]

            x = range(len(categories))
            width = 0.35

            ax.bar([i - width/2 for i in x], sessions, width, label='Sessions', color='#3498db')
            ax.bar([i + width/2 for i in x], revenues, width, label='Revenue (DZD)', color='#27ae60')

            ax.set_xlabel('Categories')
            ax.set_ylabel('Count / Revenue')
            ax.set_title('Sessions and Revenue by Category')
            ax.set_xticks(x)
            ax.set_xticklabels([cat.replace('_', ' ').title() for cat in categories])
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'No category data', ha='center', va='center', transform=ax.transAxes)

        fig.tight_layout()
        self.category_bar_chart_frame.layout().addWidget(canvas)

    def update_device_charts(self, stats):
        """Update device tab charts."""
        self.update_device_pie_chart(stats)
        self.update_device_revenue_chart(stats)

    def update_device_pie_chart(self, stats):
        """Update device usage pie chart."""
        # Clear previous chart
        for i in reversed(range(self.device_pie_chart_frame.layout().count())):
            child = self.device_pie_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        # Create matplotlib figure
        fig = Figure(figsize=(3.5, 2.5), dpi=80)
        canvas = FigureCanvas(fig)

        # Create pie chart
        ax = fig.add_subplot(111)

        device_usage = stats.get('device_usage', {})
        if device_usage:
            labels = list(device_usage.keys())
            sizes = list(device_usage.values())
            colors = ['#e74c3c', '#3498db', '#f39c12', '#9b59b6', '#1abc9c']

            ax.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors[:len(labels)])
        else:
            ax.text(0.5, 0.5, 'No device data', ha='center', va='center', transform=ax.transAxes)

        ax.set_title('Device Usage Distribution', fontweight='bold')
        fig.tight_layout()
        self.device_pie_chart_frame.layout().addWidget(canvas)

    def update_device_revenue_chart(self, stats):
        """Update device revenue bar chart."""
        # Clear previous chart
        for i in reversed(range(self.device_revenue_chart_frame.layout().count())):
            child = self.device_revenue_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        # Create matplotlib figure
        fig = Figure(figsize=(4, 2.5), dpi=80)
        canvas = FigureCanvas(fig)

        # Create bar chart
        ax = fig.add_subplot(111)

        device_revenue = stats.get('device_revenue', {})
        if device_revenue:
            devices = list(device_revenue.keys())
            revenues = list(device_revenue.values())

            bars = ax.bar(devices, revenues, color=['#e74c3c', '#3498db', '#f39c12', '#9b59b6', '#1abc9c'][:len(devices)])
            ax.set_xlabel('Devices')
            ax.set_ylabel('Revenue (DZD)')
            ax.set_title('Revenue by Device')
            ax.grid(True, alpha=0.3)

            # Rotate x-axis labels if needed
            plt.setp(ax.get_xticklabels(), rotation=45)
        else:
            ax.text(0.5, 0.5, 'No device revenue data', ha='center', va='center', transform=ax.transAxes)

        fig.tight_layout()
        self.device_revenue_chart_frame.layout().addWidget(canvas)

    def update_time_charts(self, stats):
        """Update time analysis tab charts."""
        self.update_hourly_chart(stats)
        self.update_weekly_chart(stats)

    def update_hourly_chart(self, stats):
        """Update hourly usage bar chart."""
        # Clear previous chart
        for i in reversed(range(self.hourly_chart_frame.layout().count())):
            child = self.hourly_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        # Create matplotlib figure
        fig = Figure(figsize=(4, 2.5), dpi=80)
        canvas = FigureCanvas(fig)

        # Create bar chart
        ax = fig.add_subplot(111)

        hourly_usage = stats.get('hourly_usage', {})
        if hourly_usage:
            hours = list(range(24))
            usage = [hourly_usage.get(hour, 0) for hour in hours]

            bars = ax.bar(hours, usage, color='#3498db', alpha=0.7)
            ax.set_xlabel('Hour of Day')
            ax.set_ylabel('Sessions')
            ax.set_title('Hourly Usage Pattern')
            ax.set_xticks(range(0, 24, 4))
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, 'No hourly data', ha='center', va='center', transform=ax.transAxes)

        fig.tight_layout()
        self.hourly_chart_frame.layout().addWidget(canvas)

    def update_weekly_chart(self, stats):
        """Update weekly revenue trend chart."""
        # Clear previous chart
        for i in reversed(range(self.weekly_chart_frame.layout().count())):
            child = self.weekly_chart_frame.layout().itemAt(i).widget()
            if isinstance(child, FigureCanvas):
                child.setParent(None)

        # Create matplotlib figure
        fig = Figure(figsize=(3.5, 2.5), dpi=80)
        canvas = FigureCanvas(fig)

        # Create line chart
        ax = fig.add_subplot(111)

        # Sample weekly data - you can replace with real data
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        revenue = [stats.get('total_revenue', 0) * 0.15 * (i+1) for i in range(7)]

        ax.plot(days, revenue, marker='o', linewidth=2, color='#e74c3c')
        ax.fill_between(days, revenue, alpha=0.3, color='#e74c3c')
        ax.set_xlabel('Day of Week')
        ax.set_ylabel('Revenue (DZD)')
        ax.set_title('Weekly Revenue Trend')
        ax.grid(True, alpha=0.3)

        fig.tight_layout()
        self.weekly_chart_frame.layout().addWidget(canvas)

    def update_ai_recommendations(self, stats):
        """Update AI recommendations using advanced AI coordinator."""
        if not self.ai_coordinator:
            self._update_fallback_recommendations(stats)
            return

        try:
            # Get products data if available
            products_data = {}
            if self.products_panel:
                products_data = self.products_panel.get_all_products_data()

            # Get comprehensive AI analysis
            analysis = self.ai_coordinator.get_comprehensive_analysis(stats, products_data)

            # Update executive summary
            executive_summary = analysis.get('executive_summary', {})
            self._update_executive_summary(executive_summary)

            # Update pricing recommendations
            pricing_analysis = analysis.get('pricing_analysis', {})
            self._update_pricing_recommendations(pricing_analysis)

            # Update promotions
            promotion_recommendations = analysis.get('promotion_recommendations', {})
            self._update_promotion_recommendations(promotion_recommendations)

            # Update anomaly detection
            anomaly_detection = analysis.get('anomaly_detection', {})
            self._update_anomaly_detection(anomaly_detection)

            # Update action items
            action_items = analysis.get('action_items', [])
            self._update_action_items(action_items)

        except Exception as e:
            print(f"AI Analysis Error: {e}")
            self._update_fallback_recommendations(stats)

    def _update_executive_summary(self, executive_summary):
        """Update executive summary display."""
        health = executive_summary.get('business_health', 'unknown')
        outlook = executive_summary.get('revenue_outlook', 'unknown')
        risk = executive_summary.get('risk_level', 'unknown')

        health_emoji = {'excellent': '🟢', 'good': '🔵', 'fair': '🟡', 'needs_attention': '🔴'}.get(health, '⚪')

        summary_text = f"""
{health_emoji} Business Health: {health.replace('_', ' ').title()}
📈 Revenue Outlook: {outlook.title()}
⚠️ Risk Level: {risk.title()}

Key Insights:
{chr(10).join('• ' + insight for insight in executive_summary.get('key_insights', []))}
"""
        self.executive_summary_text.setText(summary_text.strip())

    def _update_pricing_recommendations(self, pricing_analysis):
        """Update pricing recommendations display."""
        if not pricing_analysis:
            self.pricing_text.setText("📊 Pricing analysis unavailable - insufficient data")
            return

        price_changes = pricing_analysis.get('price_changes', {})
        profit_prediction = pricing_analysis.get('profit_prediction', {})

        pricing_text = "🎯 OPTIMIZED PRICING RECOMMENDATIONS:\n\n"

        for device, change in price_changes.items():
            change_pct = change.get('change_percent', 0)
            if abs(change_pct) > 5:
                arrow = "📈" if change_pct > 0 else "📉"
                pricing_text += f"{arrow} {device.replace('_', ' ').title()}: {change['current']:.2f} → {change['optimized']:.2f} DZD ({change_pct:+.1f}%)\n"

        if profit_prediction:
            total_profit = profit_prediction.get('total_profit', 0)
            profit_margin = profit_prediction.get('profit_margin', 0)
            pricing_text += f"\n💰 Predicted Profit: {total_profit:.2f} DZD ({profit_margin:.1f}% margin)"

        self.pricing_text.setText(pricing_text)

    def _update_promotion_recommendations(self, promotion_recommendations):
        """Update promotion recommendations display."""
        promotions = promotion_recommendations.get('promotions', [])
        vip_customers = promotion_recommendations.get('vip_customers', [])

        promo_text = "🎯 SMART PROMOTION OPPORTUNITIES:\n\n"

        for promo in promotions[:5]:  # Top 5 promotions
            promo_type = promo.get('type', 'general').replace('_', ' ').title()
            promo_text += f"• {promo_type}: {promo.get('promotion', 'N/A')}\n"

        if vip_customers:
            promo_text += f"\n👑 VIP CUSTOMERS DETECTED: {len(vip_customers)} high-value customers identified\n"
            for vip in vip_customers[:3]:
                promo_text += f"• {vip.get('recommendation', 'N/A')}\n"

        self.promotions_text.setText(promo_text)

    def _update_anomaly_detection(self, anomaly_detection):
        """Update anomaly detection display."""
        is_anomaly = anomaly_detection.get('is_anomaly', False)
        anomalies = anomaly_detection.get('anomalies_detected', [])
        consensus_score = anomaly_detection.get('consensus_score', 0)

        if is_anomaly:
            anomaly_text = f"🚨 ANOMALIES DETECTED (Score: {consensus_score:.2f}):\n\n"
            for anomaly in anomalies:
                anomaly_text += f"⚠️ {anomaly.get('description', 'Unknown anomaly')}\n"

            recommendations = anomaly_detection.get('recommendations', [])
            if recommendations:
                anomaly_text += "\n📋 IMMEDIATE ACTIONS:\n"
                for rec in recommendations:
                    anomaly_text += f"• {rec}\n"
        else:
            anomaly_text = f"✅ NO ANOMALIES DETECTED (Score: {consensus_score:.2f})\n\nBusiness operations appear normal. All metrics within expected ranges."

        self.anomaly_text.setText(anomaly_text)

    def _update_action_items(self, action_items):
        """Update action items display."""
        if not action_items:
            self.action_items_text.setText("📋 No priority actions required at this time.")
            return

        action_text = "📋 PRIORITY ACTION ITEMS:\n\n"

        priority_emojis = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}

        for item in action_items[:8]:  # Top 8 action items
            priority = item.get('priority', 'low')
            emoji = priority_emojis.get(priority, '⚪')
            title = item.get('title', 'Unknown')
            timeline = item.get('timeline', 'unknown')

            action_text += f"{emoji} {title} ({timeline})\n"
            action_text += f"   {item.get('description', 'No description')}\n\n"

        self.action_items_text.setText(action_text)

    def _update_fallback_recommendations(self, stats):
        """Fallback recommendations when AI system fails."""
        self.executive_summary_text.setText("⚠️ AI system temporarily unavailable. Using basic analysis.")
        self.pricing_text.setText("📊 Advanced pricing optimization unavailable.")
        self.promotions_text.setText("🎯 Smart promotions system offline.")
        self.anomaly_text.setText("⚠️ Anomaly detection unavailable.")
        self.action_items_text.setText("📋 Priority actions cannot be determined without AI analysis.")



    def generate_business_insights(self, stats, products_data):
        """Generate AI-powered business insights."""
        insights = []

        total_revenue = stats.get('total_revenue', 0)
        total_sessions = stats.get('total_sessions', 0)
        sessions_by_type = stats.get('sessions_by_type', {})

        # Revenue analysis
        if total_revenue > 0:
            avg_session_value = total_revenue / max(total_sessions, 1)
            insights.append(f"💰 Average session value: {avg_session_value:.2f} DZD")

            if avg_session_value > 100:
                insights.append("✅ Strong monetization - customers are spending well per session")
            elif avg_session_value > 50:
                insights.append("⚠️ Moderate monetization - room for improvement in session value")
            else:
                insights.append("🔴 Low session value - consider pricing strategy review")

        # Session type analysis
        if sessions_by_type:
            most_popular = max(sessions_by_type.items(), key=lambda x: x[1])
            insights.append(f"🎮 Most popular activity: {most_popular[0].replace('_', ' ').title()} ({most_popular[1]} sessions)")

            # Device utilization
            device_count = len(sessions_by_type)
            if device_count >= 4:
                insights.append("📈 Excellent device variety - good diversification")
            elif device_count >= 2:
                insights.append("📊 Moderate device usage - consider expanding popular categories")
            else:
                insights.append("📉 Limited device usage - focus on marketing underutilized devices")

        # Products analysis
        if products_data and products_data.get('total_sales', 0) > 0:
            product_revenue = products_data.get('total_revenue', 0)
            product_sales = products_data.get('total_sales', 0)
            avg_product_sale = product_revenue / max(product_sales, 1)

            insights.append(f"🛒 Product sales: {product_sales} items, {product_revenue:.2f} DZD revenue")
            insights.append(f"📦 Average product value: {avg_product_sale:.2f} DZD")

            if product_revenue > total_revenue * 0.3:
                insights.append("🌟 Strong product sales - excellent secondary revenue stream")
            elif product_revenue > total_revenue * 0.1:
                insights.append("📈 Good product performance - potential for growth")
            else:
                insights.append("💡 Untapped product potential - focus on product marketing")

        return "\n\n".join(insights) if insights else "📊 Analyzing data... More insights available with increased activity."

    def generate_revenue_predictions(self, stats, products_data):
        """Generate AI-powered revenue predictions."""
        predictions = []

        total_revenue = stats.get('total_revenue', 0)
        total_sessions = stats.get('total_sessions', 0)

        if total_revenue > 0 and total_sessions > 0:
            # Simple trend analysis
            daily_avg = total_revenue / 7  # Assuming 7-day period
            weekly_prediction = daily_avg * 7
            monthly_prediction = daily_avg * 30

            predictions.append(f"📈 Predicted weekly revenue: {weekly_prediction:.2f} DZD")
            predictions.append(f"📅 Predicted monthly revenue: {monthly_prediction:.2f} DZD")

            # Growth scenarios
            conservative_growth = monthly_prediction * 1.05
            optimistic_growth = monthly_prediction * 1.15

            predictions.append(f"🎯 Conservative growth target: {conservative_growth:.2f} DZD (+5%)")
            predictions.append(f"🚀 Optimistic growth target: {optimistic_growth:.2f} DZD (+15%)")

            # Session-based predictions
            avg_session_value = total_revenue / total_sessions
            if avg_session_value > 0:
                target_sessions = 100  # Target sessions per week
                potential_revenue = target_sessions * avg_session_value
                predictions.append(f"💡 With {target_sessions} sessions/week: {potential_revenue:.2f} DZD potential")

        # Products predictions
        if products_data and products_data.get('total_revenue', 0) > 0:
            product_revenue = products_data.get('total_revenue', 0)
            product_growth = product_revenue * 1.2
            predictions.append(f"🛒 Product revenue growth potential: {product_growth:.2f} DZD (+20%)")

        return "\n\n".join(predictions) if predictions else "🔮 Building prediction models... More data needed for accurate forecasts."

    def generate_strategic_recommendations(self, stats, products_data):
        """Generate AI-powered strategic recommendations."""
        recommendations = []

        total_revenue = stats.get('total_revenue', 0)
        total_sessions = stats.get('total_sessions', 0)
        sessions_by_type = stats.get('sessions_by_type', {})
        hourly_usage = stats.get('hourly_usage', {})

        # Revenue optimization
        if total_revenue > 0 and total_sessions > 0:
            avg_session_value = total_revenue / total_sessions

            if avg_session_value < 50:
                recommendations.append("💰 PRICING STRATEGY: Consider implementing premium packages or extending session durations to increase average session value.")

            if avg_session_value > 100:
                recommendations.append("🎯 UPSELLING: High session values indicate willingness to pay - introduce VIP packages or exclusive experiences.")

        # Device optimization
        if sessions_by_type:
            sorted_devices = sorted(sessions_by_type.items(), key=lambda x: x[1], reverse=True)

            if len(sorted_devices) > 1:
                top_device = sorted_devices[0]
                bottom_device = sorted_devices[-1]

                recommendations.append(f"🎮 DEVICE FOCUS: {top_device[0].replace('_', ' ').title()} is your star performer. Consider adding more similar devices or premium versions.")

                if bottom_device[1] < top_device[1] * 0.3:
                    recommendations.append(f"⚠️ UNDERPERFORMING DEVICE: {bottom_device[0].replace('_', ' ').title()} needs attention. Consider special promotions or repositioning.")

        # Time-based optimization
        if hourly_usage:
            peak_hours = [hour for hour, usage in hourly_usage.items() if usage > 0]
            if peak_hours:
                peak_start = min(peak_hours)
                peak_end = max(peak_hours)
                recommendations.append(f"⏰ PEAK HOURS: Optimize staffing and pricing for {peak_start}:00-{peak_end}:00. Consider dynamic pricing during peak times.")

                if len(peak_hours) < 8:
                    recommendations.append("📈 EXPAND HOURS: Low off-peak usage detected. Implement promotions or events to increase utilization during quiet hours.")

        # Products recommendations
        if products_data:
            total_products = products_data.get('total_products', 0)
            product_revenue = products_data.get('total_revenue', 0)

            if total_products > 0 and product_revenue < total_revenue * 0.2:
                recommendations.append("🛒 PRODUCT STRATEGY: Product sales are below potential. Implement combo deals, loyalty programs, or strategic product placement.")

            if total_products < 10:
                recommendations.append("📦 INVENTORY EXPANSION: Limited product variety detected. Consider expanding inventory with high-margin items like energy drinks and premium snacks.")

        # General business recommendations
        if total_sessions < 50:
            recommendations.append("📢 MARKETING BOOST: Low session count indicates need for increased marketing efforts. Focus on social media, local partnerships, and referral programs.")

        if total_sessions > 100:
            recommendations.append("🏆 SCALING OPPORTUNITY: High session volume indicates strong demand. Consider expanding capacity, adding new devices, or opening additional locations.")

        return "\n\n".join(recommendations) if recommendations else "💡 Gathering strategic insights... Recommendations will improve with more operational data."

    def generate_optimization_tips(self, stats, products_data):
        """Generate AI-powered performance optimization tips."""
        tips = []

        total_revenue = stats.get('total_revenue', 0)
        total_sessions = stats.get('total_sessions', 0)
        sessions_by_type = stats.get('sessions_by_type', {})

        # Operational efficiency
        tips.append("⚡ OPERATIONAL EFFICIENCY:")
        tips.append("• Implement automated session tracking to reduce manual errors")
        tips.append("• Use QR codes for quick device access and payment processing")
        tips.append("• Set up automated notifications for session time limits")

        # Customer experience
        tips.append("\n🎯 CUSTOMER EXPERIENCE:")
        tips.append("• Create loyalty programs with points for frequent visitors")
        tips.append("• Offer group packages for friends and tournaments")
        tips.append("• Implement online booking system to reduce wait times")

        # Revenue optimization
        tips.append("\n💰 REVENUE OPTIMIZATION:")
        if total_revenue > 0:
            tips.append("• Bundle gaming sessions with food/drinks for higher margins")
            tips.append("• Implement dynamic pricing for peak vs off-peak hours")
            tips.append("• Create membership tiers with exclusive benefits")
        else:
            tips.append("• Start with competitive introductory pricing to attract customers")
            tips.append("• Focus on word-of-mouth marketing and social media presence")

        # Technology integration
        tips.append("\n🔧 TECHNOLOGY INTEGRATION:")
        tips.append("• Use this management system's analytics to track trends")
        tips.append("• Implement digital payment systems for faster transactions")
        tips.append("• Set up social media integration for sharing achievements")

        # Inventory management
        if products_data and products_data.get('total_products', 0) > 0:
            tips.append("\n📦 INVENTORY MANAGEMENT:")
            tips.append("• Track product sales patterns to optimize stock levels")
            tips.append("• Negotiate bulk purchase deals with suppliers")
            tips.append("• Implement automatic reorder points for popular items")

        # Marketing automation
        tips.append("\n📢 MARKETING AUTOMATION:")
        tips.append("• Send automated follow-up messages to inactive customers")
        tips.append("• Create targeted promotions based on customer preferences")
        tips.append("• Use data analytics to identify and reward top customers")

        # Performance monitoring
        tips.append("\n📊 PERFORMANCE MONITORING:")
        tips.append("• Review these AI recommendations weekly for continuous improvement")
        tips.append("• Set monthly revenue and session targets based on predictions")
        tips.append("• Monitor competitor pricing and adjust strategies accordingly")

        return "\n".join(tips)
