#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the configuration system.
"""

import sys
import os

# Set UTF-8 encoding for Windows console
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import ConfigManager

def test_config_manager():
    """Test the configuration manager functionality."""
    print("Testing Configuration Manager...")
    
    # Create config manager
    config = ConfigManager("test_config.json")
    
    # Test default values
    print(f"Default shop name: {config.get_shop_name()}")
    print(f"Default window size: {config.get_window_size()}")
    print(f"Default min window size: {config.get_min_window_size()}")
    
    # Test setting shop name
    original_name = config.get_shop_name()
    test_name = "My Custom Game Shop"
    
    print(f"\nChanging shop name from '{original_name}' to '{test_name}'")
    config.set_shop_name(test_name)
    
    # Verify the change
    new_name = config.get_shop_name()
    print(f"New shop name: {new_name}")
    
    if new_name == test_name:
        print("✅ Shop name change successful!")
    else:
        print("❌ Shop name change failed!")
    
    # Test setting other values
    config.set("business_settings", "currency", "USD")
    currency = config.get("business_settings", "currency")
    print(f"Currency changed to: {currency}")
    
    # Test section operations
    display_settings = config.get_section("display_settings")
    print(f"Display settings: {display_settings}")
    
    # Reset to original name
    config.set_shop_name(original_name)
    print(f"\nReset shop name back to: {config.get_shop_name()}")
    
    # Clean up test file
    if os.path.exists("test_config.json"):
        os.remove("test_config.json")
        print("Test config file cleaned up.")
    
    print("\n✅ Configuration system test completed successfully!")

if __name__ == "__main__":
    test_config_manager()