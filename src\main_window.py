"""
Main window for the Ωmega GameZone application.
"""

from PySide2.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QPushButton, QStackedWidget, QLabel, QFrame,
                               QMessageBox, QApplication)
from PySide2.QtCore import Qt, QTimer
from PySide2.QtGui import QFont, QPalette, QColor, QPixmap, QPainter, QBrush
import os

from .models.data_manager import DataManager
from .utils.config_manager import ConfigManager
from .utils.translation_manager import get_translation_manager, tr
from .panels.ps5_panel import PS5Panel
from .panels.arcade_panel import ArcadePanel
from .panels.babyfoot_panel import BabyfootPanel
from .panels.pool_panel import PoolPanel
from .panels.other_panel import OtherPanel
from .panels.products_panel import ProductsPanel
from .panels.statistics_panel import StatisticsPanel
from .dialogs.settings_dialog import SettingsDialog

class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.data_manager = DataManager()

        # Initialize translation manager
        self.translation_manager = get_translation_manager(self.config_manager)
        self.translation_manager.language_changed.connect(self.on_language_changed)

        self.setup_ui()
        self.setup_timer()
        
    def setup_ui(self):
        """Set up the user interface."""
        shop_name = self.config_manager.get_shop_name()
        self.setWindowTitle(shop_name)
        
        min_width, min_height = self.config_manager.get_min_window_size()
        width, height = self.config_manager.get_window_size()
        self.setMinimumSize(min_width, min_height)
        self.resize(width, height)

        # Set up the central widget with background
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        self.setup_background(central_widget)
        
        # Main layout with smaller margins to show more background
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(30, 20, 30, 20)
        main_layout.setSpacing(15)
        
        # Header
        header_layout = self.create_header()
        main_layout.addLayout(header_layout)
        
        # Content area with spacing to show background
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)
        main_layout.addLayout(content_layout)
        
        # Navigation panel
        nav_panel = self.create_navigation_panel()
        content_layout.addWidget(nav_panel)
        
        # Main content area
        self.stacked_widget = QStackedWidget()
        content_layout.addWidget(self.stacked_widget)
        
        # Create panels
        self.create_panels()
        
        # Set initial panel
        self.show_ps5_panel()
        
        # Apply styling
        self.apply_styling()

        # Set wallpaper on main window
        self.set_main_window_background()

    def setup_background(self, widget):
        """Set up background wallpaper."""
        # Don't set background on central widget, it will be set on main window
        pass

    def set_main_window_background(self):
        """Set background wallpaper on the main window."""
        wallpaper_path = os.path.join(os.path.dirname(__file__), "images", "wallpaper.jpg")
        if os.path.exists(wallpaper_path):
            # Use QPalette approach which works better and doesn't inherit
            palette = self.palette()
            pixmap = QPixmap(wallpaper_path)
            if not pixmap.isNull():
                # Scale pixmap to window size
                scaled_pixmap = pixmap.scaled(1400, 900, Qt.KeepAspectRatioByExpanding, Qt.SmoothTransformation)
                palette.setBrush(QPalette.Window, QBrush(scaled_pixmap))
                self.setPalette(palette)

                # Also set the central widget to have a transparent background so wallpaper shows through
                central_widget = self.centralWidget()
                central_widget.setStyleSheet("background-color: transparent;")

                print(f"Wallpaper set successfully from: {wallpaper_path}")
            else:
                print(f"Failed to load wallpaper: {wallpaper_path}")
        else:
            print(f"Main window wallpaper not found at: {wallpaper_path}")

    def create_header(self):
        """Create the header with title and statistics button."""
        header_layout = QHBoxLayout()
        
        # Title
        shop_name = self.config_manager.get_shop_name()
        self.title_label = QLabel(shop_name)
        title_font = QFont("Impact", 30)  # Bold, impactful font and bigger size
        title_font.setBold(True)
        title_font.setWeight(QFont.ExtraBold)  # Extra bold weight
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet("""
            color: white;
            margin: 15px;
            font-weight: 900;
            background-color: rgba(0,0,0,0.2);
            border-radius: 8px;
            padding: 8px 16px;
        """)

        # Statistics button
        self.stats_button = QPushButton()
        self.stats_button.setMinimumSize(150, 40)
        self.stats_button.setProperty("class", "info")
        self.stats_button.clicked.connect(self.show_statistics_panel)

        # Settings button
        self.settings_button = QPushButton()
        self.settings_button.setMinimumSize(150, 40)
        self.settings_button.setProperty("class", "warning")
        self.settings_button.clicked.connect(self.show_settings_dialog)

        # Update button texts
        self.update_header_texts()
        
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.settings_button)
        header_layout.addWidget(self.stats_button)
        
        return header_layout
        
    def create_navigation_panel(self):
        """Create the navigation panel with buttons for each section."""
        self.nav_frame = QFrame()
        self.nav_frame.setFrameStyle(QFrame.StyledPanel)
        self.nav_frame.setMinimumWidth(180)
        self.nav_frame.setMaximumWidth(200)
        
        nav_layout = QVBoxLayout(self.nav_frame)
        nav_layout.setContentsMargins(10, 10, 10, 10)
        nav_layout.setSpacing(10)
        
        # Navigation title
        self.nav_title = QLabel()
        self.nav_title.setFont(QFont("Arial", 14, QFont.Bold))
        self.nav_title.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(self.nav_title)

        # Navigation buttons
        self.nav_buttons = {}

        buttons_config = [
            ("ps5", self.show_ps5_panel, "ps5.png"),
            ("arcade", self.show_arcade_panel, "arcade.png"),
            ("babyfoot", self.show_babyfoot_panel, "simerace.png"),
            ("pool", self.show_pool_panel, "arcade.png"),
            ("products", self.show_products_panel, "arcade.png"),
            ("other", self.show_other_panel, "arcade.png")
        ]

        for text_key, callback, image_file in buttons_config:
            button = QPushButton()
            button.setMinimumHeight(60)
            button.clicked.connect(callback)

            # Create button layout with image and text
            button_layout = QHBoxLayout()
            button_layout.setContentsMargins(10, 5, 10, 5)

            # Add image
            image_label = QLabel()
            current_dir = os.path.dirname(__file__)
            image_path = os.path.join(current_dir, "images", image_file)
            if os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                scaled_pixmap = pixmap.scaled(30, 30, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label.setPixmap(scaled_pixmap)
            else:
                # Fallback to emoji
                emoji_map = {"ps5": "🎮", "arcade": "🕹️", "babyfoot": "⚽", "pool": "🎱", "products": "🛒", "other": "📦"}
                image_label.setText(emoji_map.get(text_key, "🎮"))
                image_label.setStyleSheet("font-size: 20px;")

            text_label = QLabel()
            text_label.setStyleSheet("font-weight: bold; margin-left: 10px;")

            button_layout.addWidget(image_label)
            button_layout.addWidget(text_label)
            button_layout.addStretch()

            # Create a widget to hold the layout
            button_widget = QWidget()
            button_widget.setLayout(button_layout)

            # Store references for translation updates
            button.text_label = text_label
            button.text_key = text_key

            nav_layout.addWidget(button)
            self.nav_buttons[text_key] = button
            
        nav_layout.addStretch()

        # Update navigation texts
        self.update_navigation_texts()

        return self.nav_frame

    def update_header_texts(self):
        """Update header button texts with translations."""
        self.stats_button.setText(f"📊 {tr('navigation.statistics')}")
        self.settings_button.setText(f"⚙️ {tr('navigation.settings')}")

    def update_navigation_texts(self):
        """Update navigation texts with translations."""
        self.nav_title.setText(tr('navigation.panels'))

        # Update navigation button texts
        for text_key, button in self.nav_buttons.items():
            if hasattr(button, 'text_label'):
                button.text_label.setText(tr(f'navigation.{text_key}'))

    def on_language_changed(self, language_code):
        """Handle language change signal."""
        # Update all translatable texts
        self.update_header_texts()
        self.update_navigation_texts()

        # Update window title if needed
        shop_name = self.config_manager.get_shop_name()
        self.setWindowTitle(shop_name)

        # Update header title
        self.title_label.setText(shop_name)

    def create_panels(self):
        """Create all the main panels."""
        # PS5 Panel
        self.ps5_panel = PS5Panel(self.data_manager)
        self.stacked_widget.addWidget(self.ps5_panel)
        
        # Arcade Panel
        self.arcade_panel = ArcadePanel(self.data_manager)
        self.stacked_widget.addWidget(self.arcade_panel)
        
        # Babyfoot Panel
        self.babyfoot_panel = BabyfootPanel(self.data_manager)
        self.stacked_widget.addWidget(self.babyfoot_panel)

        # Pool Panel
        self.pool_panel = PoolPanel(self.data_manager)
        self.stacked_widget.addWidget(self.pool_panel)

        # Products Panel
        self.products_panel = ProductsPanel(self.data_manager)
        self.stacked_widget.addWidget(self.products_panel)

        # Other Panel
        self.other_panel = OtherPanel(self.data_manager)
        self.stacked_widget.addWidget(self.other_panel)
        
        # Statistics Panel (pass products panel reference)
        self.statistics_panel = StatisticsPanel(self.data_manager, self.products_panel)
        self.stacked_widget.addWidget(self.statistics_panel)
        
    def show_ps5_panel(self):
        """Show the PS5 panel."""
        self.stacked_widget.setCurrentWidget(self.ps5_panel)
        self.update_nav_buttons("PS5")

    def show_arcade_panel(self):
        """Show the Arcade panel."""
        self.stacked_widget.setCurrentWidget(self.arcade_panel)
        self.update_nav_buttons("Arcade")

    def show_babyfoot_panel(self):
        """Show the Babyfoot panel."""
        self.stacked_widget.setCurrentWidget(self.babyfoot_panel)
        self.update_nav_buttons("Babyfoot")

    def show_pool_panel(self):
        """Show the Pool panel."""
        self.stacked_widget.setCurrentWidget(self.pool_panel)
        self.update_nav_buttons("Pool")

    def show_products_panel(self):
        """Show the Products panel."""
        self.stacked_widget.setCurrentWidget(self.products_panel)
        self.update_nav_buttons("Products")

    def show_other_panel(self):
        """Show the Other panel."""
        self.stacked_widget.setCurrentWidget(self.other_panel)
        self.update_nav_buttons("Other")
        
    def show_statistics_panel(self):
        """Show the Statistics panel."""
        self.statistics_panel.refresh_statistics()
        self.stacked_widget.setCurrentWidget(self.statistics_panel)
        self.update_nav_buttons(None)  # No nav button for statistics
        
    def show_settings_dialog(self):
        """Show the Settings dialog."""
        dialog = SettingsDialog(self.config_manager, self)
        dialog.settings_changed.connect(self.on_settings_changed)
        dialog.exec_()
        
    def on_settings_changed(self):
        """Handle settings changes."""
        # Update window title with new shop name
        shop_name = self.config_manager.get_shop_name()
        self.setWindowTitle(shop_name)
        
        # Update header title
        self.update_header_title()
        
    def update_header_title(self):
        """Update the header title label."""
        # Find the title label in the header and update it
        header_layout = self.centralWidget().layout().itemAt(0).layout()
        title_label = header_layout.itemAt(0).widget()
        if isinstance(title_label, QLabel):
            shop_name = self.config_manager.get_shop_name()
            title_label.setText(shop_name)
        
    def update_nav_buttons(self, active_button_text):
        """Update navigation button styles."""
        for text, button in self.nav_buttons.items():
            if text == active_button_text:
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #2c3e50;
                        color: white;
                        border: 3px solid #34495e;
                        border-radius: 8px;
                        font-weight: bold;
                        font-size: 14px;
                        padding: 12px 16px;
                    }
                    QPushButton:hover {
                        background-color: #34495e;
                        border-color: #4a6741;
                        color: #ecf0f1;
                    }
                    QPushButton:pressed {
                        background-color: #1a252f;
                        border-color: #2c3e50;
                        padding: 13px 15px 11px 17px;
                    }
                """)
            else:
                button.setStyleSheet("""
                    QPushButton {
                        background-color: transparent;
                        color: white;
                        border: 3px solid #bdc3c7;
                        border-radius: 8px;
                        font-weight: normal;
                        font-size: 14px;
                        padding: 12px 16px;
                    }
                    QPushButton:hover {
                        background-color: rgba(255, 255, 255, 0.15);
                        border-color: #95a5a6;
                        color: #ecf0f1;
                    }
                    QPushButton:pressed {
                        background-color: rgba(255, 255, 255, 0.08);
                        border-color: #7f8c8d;
                        padding: 13px 15px 11px 17px;
                    }
                """)
                
    def setup_timer(self):
        """Set up timer for regular updates."""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_panels)
        self.update_timer.start(1000)  # Update every second
        
    def update_panels(self):
        """Update all panels with current data."""
        current_widget = self.stacked_widget.currentWidget()
        if hasattr(current_widget, 'update_display'):
            current_widget.update_display()
            
    def apply_styling(self):
        """Apply global styling to the application."""
        # Apply comprehensive styling to ensure text visibility
        self.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                color: #333;
                border: 2px solid #ccc;
                border-radius: 6px;
                padding: 8px 14px;
                font-size: 12px;
                font-weight: 500;
                min-height: 26px;
                max-height: 32px;
            }

            QPushButton:hover {
                background-color: #e8e8e8;
                border-color: #888;
                color: #222;
            }

            QPushButton:pressed {
                background-color: #d8d8d8;
                border-color: #666;
                padding: 9px 13px 7px 15px;  /* Simulate pressed effect */
            }

            QFrame {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
            }

            QScrollArea {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
            }

            QLabel {
                color: #2c3e50;
                background-color: transparent;
                font-weight: normal;
            }

            QStackedWidget {
                background-color: #f8f9fa;
                border-radius: 8px;
            }

            QStackedWidget > QWidget {
                background-color: #f8f9fa;
            }

            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #e1e8ed;
            }

            QTabWidget::pane {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
            }

            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }

            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
            }

            /* Primary action buttons */
            QPushButton[class="primary"] {
                background-color: #4CAF50;
                color: white;
                border: 2px solid #45a049;
                font-weight: bold;
            }

            QPushButton[class="primary"]:hover {
                background-color: #45a049;
                border-color: #3d8b40;
                color: #f1f8e9;
            }

            QPushButton[class="primary"]:pressed {
                background-color: #3d8b40;
                border-color: #2e7d32;
                padding: 9px 13px 7px 15px;
            }

            /* Danger buttons */
            QPushButton[class="danger"] {
                background-color: #f44336;
                color: white;
                border: 2px solid #da190b;
                font-weight: bold;
            }

            QPushButton[class="danger"]:hover {
                background-color: #da190b;
                border-color: #c62828;
                color: #ffebee;
            }

            QPushButton[class="danger"]:pressed {
                background-color: #c62828;
                border-color: #b71c1c;
                padding: 9px 13px 7px 15px;
            }

            /* Warning buttons */
            QPushButton[class="warning"] {
                background-color: #ff9800;
                color: white;
                border: 2px solid #e68900;
                font-weight: bold;
            }

            QPushButton[class="warning"]:hover {
                background-color: #e68900;
                border-color: #cc7a00;
                color: #fff3e0;
            }

            QPushButton[class="warning"]:pressed {
                background-color: #cc7a00;
                border-color: #b26500;
                padding: 9px 13px 7px 15px;
            }

            /* Info buttons */
            QPushButton[class="info"] {
                background-color: #2196F3;
                color: white;
                border: 2px solid #0b7dda;
                font-weight: bold;
            }

            QPushButton[class="info"]:hover {
                background-color: #0b7dda;
                border-color: #0a6ebd;
                color: #e3f2fd;
            }

            QPushButton[class="info"]:pressed {
                background-color: #0a6ebd;
                border-color: #0d47a1;
                padding: 9px 13px 7px 15px;
            }
        """)
        
    def closeEvent(self, event):
        """Handle application close event."""
        reply = QMessageBox.question(
            self, 'Exit Application',
            'Are you sure you want to exit?\nAll data will be saved automatically.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Save data before closing
            self.data_manager.save_data()
            event.accept()
        else:
            event.ignore()
