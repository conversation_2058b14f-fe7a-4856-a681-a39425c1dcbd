"""
Promotion Engine using Experta rule engine
"""

from experta import *
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List


class BusinessFact(Fact):
    """Facts about the business state."""
    pass


class PromotionEngine(KnowledgeEngine):
    """Expert system for generating dynamic promotions and recommendations."""
    
    def __init__(self):
        super().__init__()
        self.recommendations = []
        self.promotions = []
        self.vip_customers = []
        
    def reset_recommendations(self):
        """Reset recommendations for new analysis."""
        self.recommendations = []
        self.promotions = []
        self.vip_customers = []
        
    # VIP Customer Detection Rules
    @Rule(BusinessFact(customer_sessions=MATCH.sessions),
          TEST(lambda sessions: sessions >= 20))
    def detect_vip_high_frequency(self, sessions):
        """Detect VIP customers based on high session frequency."""
        self.vip_customers.append({
            'type': 'high_frequency',
            'sessions': sessions,
            'recommendation': f'VIP Status: {sessions} sessions - Offer premium membership'
        })
        
    @Rule(BusinessFact(customer_spending=MATCH.spending),
          TEST(lambda spending: spending >= 2000))
    def detect_vip_high_spending(self, spending):
        """Detect VIP customers based on high spending."""
        self.vip_customers.append({
            'type': 'high_spending',
            'spending': spending,
            'recommendation': f'VIP Status: {spending:.2f} DZD spent - Offer exclusive perks'
        })
    
    # Low Utilization Rules
    @Rule(BusinessFact(device_utilization=MATCH.util, device_type=MATCH.device),
          TEST(lambda util: util < 30))
    def low_device_utilization(self, util, device):
        """Generate promotions for underutilized devices."""
        discount = min(30, int((30 - util) * 2))  # Up to 30% discount
        self.promotions.append({
            'type': 'low_utilization',
            'device': device,
            'utilization': util,
            'discount': discount,
            'promotion': f'{discount}% OFF {device.replace("_", " ").title()} - Limited Time!'
        })
        
    @Rule(BusinessFact(hourly_usage=MATCH.usage, hour=MATCH.hour),
          TEST(lambda usage: usage < 2))
    def idle_time_promotion(self, usage, hour):
        """Generate promotions for idle time periods."""
        if 9 <= hour <= 17:  # Business hours
            self.promotions.append({
                'type': 'idle_time',
                'hour': hour,
                'usage': usage,
                'promotion': f'Happy Hour {hour}:00-{hour+1}:00 - 25% OFF all devices!'
            })
            
    # Revenue Optimization Rules
    @Rule(BusinessFact(daily_revenue=MATCH.revenue, target_revenue=MATCH.target),
          TEST(lambda revenue, target: revenue < target * 0.8))
    def low_daily_revenue(self, revenue, target):
        """Generate promotions when daily revenue is low."""
        self.promotions.append({
            'type': 'revenue_boost',
            'current_revenue': revenue,
            'target_revenue': target,
            'promotion': 'Flash Sale: Buy 2 hours, get 1 hour FREE!'
        })
        
    @Rule(BusinessFact(session_duration=MATCH.duration),
          TEST(lambda duration: duration < 60))
    def short_session_upsell(self, duration):
        """Recommend upselling for short sessions."""
        self.recommendations.append({
            'type': 'upsell',
            'current_duration': duration,
            'recommendation': 'Offer session extensions with 20% discount for next hour'
        })
        
    # Product Sales Rules
    @Rule(BusinessFact(product_sales_ratio=MATCH.ratio),
          TEST(lambda ratio: ratio < 0.15))
    def low_product_sales(self, ratio):
        """Generate promotions to boost product sales."""
        self.promotions.append({
            'type': 'product_boost',
            'current_ratio': ratio,
            'promotion': 'Gaming Combo: Any session + drink + snack for 15% OFF total'
        })
        
    @Rule(BusinessFact(popular_product=MATCH.product, sales_count=MATCH.count),
          TEST(lambda count: count > 10))
    def popular_product_bundle(self, product, count):
        """Create bundles with popular products."""
        self.promotions.append({
            'type': 'popular_bundle',
            'product': product,
            'sales_count': count,
            'promotion': f'Gamer\'s Choice: {product} + 2hr session combo deal!'
        })
        
    # Competitive Strategy Rules
    @Rule(BusinessFact(competitor_price=MATCH.comp_price, our_price=MATCH.our_price),
          TEST(lambda comp_price, our_price: our_price > comp_price * 1.1))
    def price_competitive_adjustment(self, comp_price, our_price):
        """Recommend price adjustments for competitiveness."""
        self.recommendations.append({
            'type': 'competitive_pricing',
            'competitor_price': comp_price,
            'our_price': our_price,
            'recommendation': f'Consider price adjustment: competitor at {comp_price:.2f} DZD'
        })
        
    # Customer Retention Rules
    @Rule(BusinessFact(days_since_last_visit=MATCH.days, customer_value=MATCH.value),
          TEST(lambda days: days > 14),
          TEST(lambda value: value > 500))
    def win_back_valuable_customer(self, days, value):
        """Generate win-back promotions for valuable customers."""
        self.promotions.append({
            'type': 'win_back',
            'days_absent': days,
            'customer_value': value,
            'promotion': f'We miss you! 30% OFF your next visit - Valid for 7 days'
        })
        
    # Peak Time Management Rules
    @Rule(BusinessFact(peak_time_demand=MATCH.demand, capacity=MATCH.capacity),
          TEST(lambda demand, capacity: demand > capacity * 0.9))
    def peak_time_pricing(self, demand, capacity):
        """Recommend peak time pricing strategies."""
        self.recommendations.append({
            'type': 'peak_pricing',
            'demand': demand,
            'capacity': capacity,
            'recommendation': 'Implement surge pricing (+20%) during peak hours'
        })
        
    @Rule(BusinessFact(waiting_customers=MATCH.waiting),
          TEST(lambda waiting: waiting > 3))
    def queue_management(self, waiting):
        """Manage customer queues with promotions."""
        self.promotions.append({
            'type': 'queue_management',
            'waiting_count': waiting,
            'promotion': 'Skip the wait! Book your next session now with 10% OFF'
        })


class PromotionAnalyzer:
    """Analyzer that feeds data to the promotion engine."""
    
    def __init__(self):
        self.engine = PromotionEngine()
        
    def analyze_and_recommend(self, business_data: Dict, 
                            products_data: Dict = None) -> Dict:
        """
        Analyze business data and generate recommendations.
        
        Args:
            business_data: Current business metrics
            products_data: Product sales data
            
        Returns:
            Dictionary with promotions and recommendations
        """
        self.engine.reset()
        self.engine.reset_recommendations()
        
        # Feed facts to the engine
        self._feed_business_facts(business_data, products_data)
        
        # Run the inference engine
        self.engine.run()
        
        return {
            'promotions': self.engine.promotions,
            'recommendations': self.engine.recommendations,
            'vip_customers': self.engine.vip_customers,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
    def _feed_business_facts(self, business_data: Dict, products_data: Dict = None):
        """Feed business facts to the expert system."""
        
        # Device utilization facts
        sessions_by_type = business_data.get('sessions_by_type', {})
        total_sessions = sum(sessions_by_type.values()) or 1
        
        for device, sessions in sessions_by_type.items():
            utilization = (sessions / total_sessions) * 100
            self.engine.declare(BusinessFact(
                device_utilization=utilization,
                device_type=device
            ))
            
        # Hourly usage facts
        hourly_usage = business_data.get('hourly_usage', {})
        for hour, usage in hourly_usage.items():
            self.engine.declare(BusinessFact(
                hourly_usage=usage,
                hour=hour
            ))
            
        # Revenue facts
        total_revenue = business_data.get('total_revenue', 0)
        target_revenue = 1000  # Default target
        self.engine.declare(BusinessFact(
            daily_revenue=total_revenue,
            target_revenue=target_revenue
        ))
        
        # Session duration facts (simulated)
        avg_session_duration = 90  # minutes
        if total_sessions > 0:
            avg_session_duration = max(30, 120 - (total_sessions * 2))
        
        self.engine.declare(BusinessFact(
            session_duration=avg_session_duration
        ))
        
        # Product sales facts
        if products_data:
            product_revenue = products_data.get('total_revenue', 0)
            if total_revenue > 0:
                product_ratio = product_revenue / total_revenue
                self.engine.declare(BusinessFact(
                    product_sales_ratio=product_ratio
                ))
                
            # Popular products
            all_sales = products_data.get('all_sales', [])
            if all_sales:
                product_counts = {}
                for sale in all_sales:
                    product = sale.get('product_name', 'Unknown')
                    product_counts[product] = product_counts.get(product, 0) + 1
                
                for product, count in product_counts.items():
                    if count > 0:
                        self.engine.declare(BusinessFact(
                            popular_product=product,
                            sales_count=count
                        ))
        
        # VIP customer simulation (based on total revenue)
        if total_revenue > 1500:
            self.engine.declare(BusinessFact(customer_spending=total_revenue))
        
        if total_sessions > 15:
            self.engine.declare(BusinessFact(customer_sessions=total_sessions))
            
        # Peak time simulation
        peak_demand = max(hourly_usage.values()) if hourly_usage else 0
        capacity = 10  # Assume 10 device capacity
        
        if peak_demand > 0:
            self.engine.declare(BusinessFact(
                peak_time_demand=peak_demand,
                capacity=capacity
            ))
            
        # Competitive pricing (simulated)
        competitor_prices = {
            'ps5_1v1': 140.0,
            'arcade': 95.0,
            'pool': 110.0
        }
        
        current_prices = {
            'ps5_1v1': 150.0,
            'arcade': 100.0,
            'pool': 120.0
        }
        
        for device in competitor_prices:
            self.engine.declare(BusinessFact(
                competitor_price=competitor_prices[device],
                our_price=current_prices[device]
            ))
