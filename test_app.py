#!/usr/bin/env python3
"""
Test script for the GameShop Manager application.
"""

import sys
import os
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.device import Device, DeviceType
from src.models.data_manager import DataManager

def test_basic_functionality():
    """Test basic functionality of the application."""
    print("Testing GameShop Manager functionality...")
    
    # Create data manager
    data_manager = DataManager("test_data.json")
    
    # Test creating devices
    print("\n1. Creating test devices...")
    
    # PS5 Regular
    ps5_regular = Device("PS5 Station 1", DeviceType.PS5_REGULAR, 300.0)
    data_manager.add_device(ps5_regular)
    print(f"Created: {ps5_regular.name} - {ps5_regular.price_per_unit} DZD/hour")
    
    # PS5 VIP
    ps5_vip = Device("PS5 VIP Station", DeviceType.PS5_VIP, 500.0)
    data_manager.add_device(ps5_vip)
    print(f"Created: {ps5_vip.name} - {ps5_vip.price_per_unit} DZD/hour")
    
    # Arcade
    arcade = Device("Street Fighter Cabinet", DeviceType.ARCADE, 200.0)
    data_manager.add_device(arcade)
    print(f"Created: {arcade.name} - {arcade.price_per_unit} DZD/hour")
    
    # Babyfoot
    babyfoot = Device("Babyfoot Table 1", DeviceType.BABYFOOT, 50.0)
    data_manager.add_device(babyfoot)
    print(f"Created: {babyfoot.name} - {babyfoot.price_per_unit} DZD/game")
    
    # Other
    other = Device("VR Station", DeviceType.OTHER, 400.0)
    data_manager.add_device(other)
    print(f"Created: {other.name} - {other.price_per_unit} DZD/hour")
    
    # Test sessions
    print("\n2. Testing sessions...")
    
    # Start session on PS5
    session1 = ps5_regular.start_session()
    print(f"Started session on {ps5_regular.name}")
    
    # Start session on babyfoot
    session2 = babyfoot.start_session()
    print(f"Started session on {babyfoot.name}")
    
    # Add games to babyfoot
    babyfoot.add_game()
    babyfoot.add_game()
    babyfoot.add_game()
    print(f"Added 3 games to {babyfoot.name}")
    
    # Test pause/resume
    ps5_regular.pause_session()
    print(f"Paused session on {ps5_regular.name}")
    
    ps5_regular.resume_session()
    print(f"Resumed session on {ps5_regular.name}")
    
    # Check current costs
    print(f"Current cost for {ps5_regular.name}: {ps5_regular.get_current_cost():.2f} DZD")
    print(f"Current cost for {babyfoot.name}: {babyfoot.get_current_cost():.2f} DZD")
    
    # End sessions
    ps5_regular.end_session()
    babyfoot.end_session()
    print("Ended sessions")
    
    # Test statistics
    print("\n3. Testing statistics...")
    stats = data_manager.get_statistics(30)
    
    print(f"Total revenue: {stats['total_revenue']:.2f} DZD")
    print(f"Total sessions: {stats['total_sessions']}")
    print(f"Revenue by type: {stats['revenue_by_type']}")
    print(f"Sessions by type: {stats['sessions_by_type']}")
    
    # Test device retrieval
    print("\n4. Testing device retrieval...")
    ps5_devices = data_manager.get_devices_by_type(DeviceType.PS5_REGULAR)
    print(f"PS5 Regular devices: {len(ps5_devices)}")
    
    all_devices = data_manager.get_all_devices()
    print(f"Total active devices: {len(all_devices)}")
    
    # Test data persistence
    print("\n5. Testing data persistence...")
    data_manager.save_data()
    print("Data saved successfully")
    
    # Create new data manager and load
    new_data_manager = DataManager("test_data.json")
    loaded_devices = new_data_manager.get_all_devices()
    print(f"Loaded {len(loaded_devices)} devices from file")
    
    # Cleanup
    try:
        os.remove("test_data.json")
        print("Test data file cleaned up")
    except:
        pass
    
    print("\n✅ All tests passed successfully!")
    return True

if __name__ == "__main__":
    try:
        test_basic_functionality()
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
