"""
History panel for displaying session history.
"""

from PySide2.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                               QLabel, QTableWidget, QTableWidgetItem, QComboBox,
                               QSpinBox, QFrame, QHeaderView, QAbstractItemView,
                               QMessageBox)
from PySide2.QtCore import Qt
from PySide2.QtGui import QFont
from datetime import datetime, timedelta
from typing import List

from ..models.device import Device, Session, SessionState, PricingMode

class HistoryPanel(QDialog):
    """Panel for displaying session history."""
    
    def __init__(self, devices: List[Device], title: str, parent=None):
        super().__init__(parent)
        self.devices = devices
        self.all_sessions = []
        self.filtered_sessions = []
        self.setup_ui(title)
        self.load_sessions()
        self.apply_filters()
        
    def setup_ui(self, title: str):
        """Set up the panel UI."""
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(1000, 600)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header_label = QLabel(title)
        header_font = QFont()
        header_font.setPointSize(16)
        header_font.setBold(True)
        header_label.setFont(header_font)
        header_label.setStyleSheet("color: #2c3e50;")
        layout.addWidget(header_label)
        
        # Filters
        filters_frame = QFrame()
        filters_frame.setFrameStyle(QFrame.StyledPanel)
        filters_layout = QHBoxLayout(filters_frame)
        
        # Device filter
        filters_layout.addWidget(QLabel("Device:"))
        self.device_combo = QComboBox()
        self.device_combo.addItem("All Devices", None)
        for device in self.devices:
            self.device_combo.addItem(device.name, device.id)
        self.device_combo.currentTextChanged.connect(self.apply_filters)
        filters_layout.addWidget(self.device_combo)
        
        # Days filter
        filters_layout.addWidget(QLabel("Last:"))
        self.days_spinbox = QSpinBox()
        self.days_spinbox.setRange(1, 365)
        self.days_spinbox.setValue(30)
        self.days_spinbox.setSuffix(" days")
        self.days_spinbox.valueChanged.connect(self.apply_filters)
        filters_layout.addWidget(self.days_spinbox)
        
        # Status filter
        filters_layout.addWidget(QLabel("Status:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["All", "Completed", "Active", "Paused"])
        self.status_combo.currentTextChanged.connect(self.apply_filters)
        filters_layout.addWidget(self.status_combo)
        
        filters_layout.addStretch()
        
        # Export button
        self.export_button = QPushButton("📊 Export CSV")
        self.export_button.setProperty("class", "info")
        self.export_button.clicked.connect(self.export_csv)
        filters_layout.addWidget(self.export_button)

        # Clear history button
        self.clear_button = QPushButton("🗑️ Clear History")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.clear_button.clicked.connect(self.clear_history)
        filters_layout.addWidget(self.clear_button)
        
        layout.addWidget(filters_frame)
        
        # Summary
        self.summary_frame = QFrame()
        self.summary_frame.setFrameStyle(QFrame.StyledPanel)
        self.summary_frame.setMaximumHeight(60)
        summary_layout = QHBoxLayout(self.summary_frame)
        
        self.total_sessions_label = QLabel("Sessions: 0")
        self.total_revenue_label = QLabel("Revenue: 0.00 DZD")
        self.total_time_label = QLabel("Time: 0h 0m")
        self.total_games_label = QLabel("Games: 0")
        
        summary_layout.addWidget(self.total_sessions_label)
        summary_layout.addWidget(self.total_revenue_label)
        summary_layout.addWidget(self.total_time_label)
        summary_layout.addWidget(self.total_games_label)
        summary_layout.addStretch()
        
        layout.addWidget(self.summary_frame)
        
        # Table
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSortingEnabled(True)
        
        # Set up columns
        columns = ["Device", "Start Time", "End Time", "Duration", "Status", "Cost", "Games"]
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # Configure column widths
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Device
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Start Time
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # End Time
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Duration
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Cost
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Games
        
        layout.addWidget(self.table)
        
        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_button)
        layout.addLayout(close_layout)
        
    def load_sessions(self):
        """Load all sessions from devices."""
        self.all_sessions = []
        
        for device in self.devices:
            # Add completed sessions
            for session in device.session_history:
                self.all_sessions.append((device, session))
                
            # Add current session if exists
            if device.current_session:
                self.all_sessions.append((device, device.current_session))
                
        # Sort by start time (newest first)
        self.all_sessions.sort(key=lambda x: x[1].start_time, reverse=True)
        
    def apply_filters(self):
        """Apply filters to the session list."""
        cutoff_date = datetime.now() - timedelta(days=self.days_spinbox.value())
        selected_device_id = self.device_combo.currentData()
        selected_status = self.status_combo.currentText()
        
        self.filtered_sessions = []
        
        for device, session in self.all_sessions:
            # Date filter
            if session.start_time < cutoff_date:
                continue
                
            # Device filter
            if selected_device_id and device.id != selected_device_id:
                continue
                
            # Status filter
            if selected_status != "All":
                if selected_status == "Completed" and session.state != SessionState.ENDED:
                    continue
                elif selected_status == "Active" and session.state != SessionState.ACTIVE:
                    continue
                elif selected_status == "Paused" and session.state != SessionState.PAUSED:
                    continue
                    
            self.filtered_sessions.append((device, session))
            
        self.update_table()
        self.update_summary()
        
    def update_table(self):
        """Update the table with filtered sessions."""
        self.table.setRowCount(len(self.filtered_sessions))
        
        for row, (device, session) in enumerate(self.filtered_sessions):
            # Device name
            self.table.setItem(row, 0, QTableWidgetItem(device.name))
            
            # Start time
            start_time = session.start_time.strftime("%Y-%m-%d %H:%M:%S")
            self.table.setItem(row, 1, QTableWidgetItem(start_time))
            
            # End time
            if session.end_time:
                end_time = session.end_time.strftime("%Y-%m-%d %H:%M:%S")
            else:
                end_time = "Active" if session.state == SessionState.ACTIVE else "Paused"
            self.table.setItem(row, 2, QTableWidgetItem(end_time))
            
            # Duration
            duration = session.get_duration()
            hours = int(duration.total_seconds() // 3600)
            minutes = int((duration.total_seconds() % 3600) // 60)
            duration_str = f"{hours}h {minutes}m"
            self.table.setItem(row, 3, QTableWidgetItem(duration_str))
            
            # Status
            status_text = session.state.value.title()
            status_item = QTableWidgetItem(status_text)
            if session.state == SessionState.ACTIVE:
                status_item.setBackground(Qt.green)
            elif session.state == SessionState.PAUSED:
                status_item.setBackground(Qt.yellow)
            elif session.state == SessionState.ENDED:
                status_item.setBackground(Qt.lightGray)
            self.table.setItem(row, 4, status_item)
            
            # Cost
            if session.state == SessionState.ENDED:
                cost = session.total_cost
            else:
                cost = session.get_current_cost()
            cost_item = QTableWidgetItem(f"{cost:.2f} DZD")
            cost_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row, 5, cost_item)
            
            # Games (for babyfoot)
            games_item = QTableWidgetItem(str(session.games_played))
            games_item.setTextAlignment(Qt.AlignCenter)
            if session.pricing_mode != PricingMode.PER_GAME:
                games_item.setText("-")
            self.table.setItem(row, 6, games_item)
            
    def update_summary(self):
        """Update the summary statistics."""
        total_sessions = len(self.filtered_sessions)
        total_revenue = 0.0
        total_duration = timedelta()
        total_games = 0
        
        for device, session in self.filtered_sessions:
            if session.state == SessionState.ENDED:
                total_revenue += session.total_cost
            else:
                total_revenue += session.get_current_cost()
                
            total_duration += session.get_duration()
            total_games += session.games_played
            
        # Format duration
        total_hours = int(total_duration.total_seconds() // 3600)
        total_minutes = int((total_duration.total_seconds() % 3600) // 60)
        
        self.total_sessions_label.setText(f"Sessions: {total_sessions}")
        self.total_revenue_label.setText(f"Revenue: {total_revenue:.2f} DZD")
        self.total_time_label.setText(f"Time: {total_hours}h {total_minutes}m")
        self.total_games_label.setText(f"Games: {total_games}")
        
    def export_csv(self):
        """Export the filtered data to CSV."""
        try:
            from PySide2.QtWidgets import QFileDialog
            import csv
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "Export History", 
                f"gameshop_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )
            
            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # Write header
                    writer.writerow(["Device", "Start Time", "End Time", "Duration (minutes)", 
                                   "Status", "Cost (DZD)", "Games"])
                    
                    # Write data
                    for device, session in self.filtered_sessions:
                        start_time = session.start_time.strftime("%Y-%m-%d %H:%M:%S")
                        end_time = session.end_time.strftime("%Y-%m-%d %H:%M:%S") if session.end_time else ""
                        duration_minutes = int(session.get_duration().total_seconds() / 60)
                        status = session.state.value.title()
                        cost = session.total_cost if session.state == SessionState.ENDED else session.get_current_cost()
                        games = session.games_played if session.pricing_mode == PricingMode.PER_GAME else ""
                        
                        writer.writerow([device.name, start_time, end_time, duration_minutes, 
                                       status, f"{cost:.2f}", games])
                        
                QMessageBox.information(self, "Export Complete", f"History exported to:\n{filename}")

        except Exception as e:
            QMessageBox.warning(self, "Export Error", f"Failed to export history:\n{str(e)}")

    def clear_history(self):
        """Clear session history for the devices."""
        reply = QMessageBox.question(
            self, 'Clear History',
            'Are you sure you want to clear all session history?\n\n'
            'This will permanently delete all completed sessions.\n'
            'Active sessions will not be affected.\n\n'
            'This action cannot be undone.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Clear session history for all devices
            cleared_count = 0
            for device in self.devices:
                cleared_count += len(device.session_history)
                device.session_history.clear()

            # Refresh the display
            self.load_sessions()
            self.apply_filters()

            QMessageBox.information(
                self, 'History Cleared',
                f'Successfully cleared {cleared_count} session records.\n'
                'Active sessions were not affected.'
            )
