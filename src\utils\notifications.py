"""
Windows notification system with sound.
"""

import os
import sys
import winsound
from PySide2.QtWidgets import QMessageBox

def show_time_up_notification(device_name, booked_time_minutes, current_cost):
    """Show Windows toast notification with sound when time is up."""
    try:
        # Try to use Windows 10 toast notifications
        import win10toast
        
        # Format time string
        hours = booked_time_minutes // 60
        minutes = booked_time_minutes % 60
        
        time_str = ""
        if hours > 0:
            time_str = f"{hours} hour{'s' if hours > 1 else ''}"
            if minutes > 0:
                time_str += f" and {minutes} minute{'s' if minutes > 1 else ''}"
        else:
            time_str = f"{minutes} minute{'s' if minutes > 1 else ''}"
        
        # Create toast notification
        toaster = win10toast.ToastNotifier()
        
        # Play notification sound first
        try:
            # Play Windows notification sound
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        except:
            pass
        
        # Show toast notification
        toaster.show_toast(
            title="⏰ GameShop - Time Up!",
            msg=f"{device_name}\nBooked time: {time_str}\nCurrent cost: {current_cost:.2f} DZD\n\nClick to return to app",
            icon_path=None,  # Use default icon
            duration=10,  # Show for 10 seconds
            threaded=True  # Don't block the main thread
        )
        
        return True
        
    except ImportError:
        # Fallback to system notification sound + message box
        try:
            winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        except:
            pass
        return False
        
    except Exception as e:
        print(f"Notification error: {e}")
        return False

def install_notification_dependencies():
    """Install required packages for Windows notifications."""
    try:
        import subprocess
        import sys
        
        # Install win10toast for Windows notifications
        subprocess.check_call([sys.executable, "-m", "pip", "install", "win10toast"])
        print("Successfully installed notification dependencies!")
        return True
    except Exception as e:
        print(f"Failed to install notification dependencies: {e}")
        return False

def test_notification():
    """Test the notification system."""
    print("Testing notification system...")
    
    # Test sound
    try:
        winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
        print("✓ Sound test successful")
    except Exception as e:
        print(f"✗ Sound test failed: {e}")
    
    # Test toast notification
    success = show_time_up_notification("Test Device", 30, 150.0)
    if success:
        print("✓ Toast notification test successful")
    else:
        print("✗ Toast notification test failed - using fallback")
    
    return success

if __name__ == "__main__":
    # Test the notification system
    test_notification()
