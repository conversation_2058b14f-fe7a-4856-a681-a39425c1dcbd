"""
Translation manager for handling internationalization.
"""

import json
import os
from typing import Dict, Any, Optional
from PySide2.QtCore import QObject, Signal

class TranslationManager(QObject):
    """Manages application translations and language switching."""
    
    language_changed = Signal(str)  # Emitted when language changes
    
    def __init__(self, config_manager=None):
        super().__init__()
        self.config_manager = config_manager
        self.translations: Dict[str, Dict[str, str]] = {}
        self.current_language = "en"
        self.translations_dir = os.path.join(os.path.dirname(__file__), "..", "translations")
        
        # Ensure translations directory exists
        os.makedirs(self.translations_dir, exist_ok=True)
        
        self.load_translations()
        
        # Set initial language from config
        if self.config_manager:
            saved_language = self.config_manager.get("app_settings", "language", "en")
            self.set_language(saved_language)
    
    def load_translations(self):
        """Load all translation files."""
        supported_languages = ["en", "fr", "ar"]
        
        for lang in supported_languages:
            translation_file = os.path.join(self.translations_dir, f"{lang}.json")
            if os.path.exists(translation_file):
                try:
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        self.translations[lang] = json.load(f)
                except Exception as e:
                    print(f"Error loading translation file {translation_file}: {e}")
                    self.translations[lang] = {}
            else:
                self.translations[lang] = {}
    
    def set_language(self, language_code: str):
        """Set the current language."""
        if language_code in self.translations:
            self.current_language = language_code
            self.language_changed.emit(language_code)
            
            # Save to config if available
            if self.config_manager:
                self.config_manager.set("app_settings", "language", language_code)
    
    def get_current_language(self) -> str:
        """Get the current language code."""
        return self.current_language
    
    def translate(self, key: str, default: str = None) -> str:
        """
        Translate a key to the current language.
        
        Args:
            key: Translation key (can use dot notation for nested keys)
            default: Default text if translation not found
        
        Returns:
            Translated text or default text or the key itself
        """
        if self.current_language not in self.translations:
            return default or key
        
        # Handle nested keys with dot notation
        keys = key.split('.')
        translation_dict = self.translations[self.current_language]
        
        try:
            for k in keys:
                translation_dict = translation_dict[k]
            return translation_dict
        except (KeyError, TypeError):
            # Fallback to English if current language doesn't have the key
            if self.current_language != "en" and "en" in self.translations:
                try:
                    translation_dict = self.translations["en"]
                    for k in keys:
                        translation_dict = translation_dict[k]
                    return translation_dict
                except (KeyError, TypeError):
                    pass
            
            return default or key
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get available languages with their display names."""
        return {
            "en": "English",
            "fr": "Français", 
            "ar": "العربية"
        }
    
    def get_language_display_name(self, language_code: str) -> str:
        """Get the display name for a language code."""
        return self.get_available_languages().get(language_code, language_code)

# Global translation manager instance
_translation_manager = None

def get_translation_manager(config_manager=None) -> TranslationManager:
    """Get the global translation manager instance."""
    global _translation_manager
    if _translation_manager is None:
        _translation_manager = TranslationManager(config_manager)
    return _translation_manager

def tr(key: str, default: str = None) -> str:
    """
    Convenience function for translation.
    
    Args:
        key: Translation key
        default: Default text if translation not found
    
    Returns:
        Translated text
    """
    global _translation_manager
    if _translation_manager is None:
        return default or key
    return _translation_manager.translate(key, default)