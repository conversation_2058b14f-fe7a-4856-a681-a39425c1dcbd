"""
Products panel for managing drinks, chips, and snacks.
"""

from PySide2.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                               QLabel, QScrollArea, QFrame, QGridLayout,
                               QDialog, QFormLayout, QLineEdit, QDoubleSpinBox,
                               QDialogButtonBox, QMessageBox, QTabWidget,
                               QSpinBox, QComboBox)
from PySide2.QtCore import Qt
from PySide2.QtGui import QFont, QPixmap
import os

class AddProductDialog(QDialog):
    """Dialog for adding a new product."""
    
    def __init__(self, category, parent=None):
        super().__init__(parent)
        self.category = category
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle(f"Add {self.category}")
        self.setModal(True)
        self.resize(350, 250)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Product name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(f"e.g., Coca Cola, Lays, Kit Kat")
        form_layout.addRow("Product Name:", self.name_edit)
        
        # Price
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.0, 1000.0)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" DZD")
        self.price_spinbox.setValue(50.0)  # Default price
        form_layout.addRow("Price:", self.price_spinbox)

        # Brand (optional)
        self.brand_edit = QLineEdit()
        self.brand_edit.setPlaceholderText("e.g., Coca-Cola, PepsiCo, Nestlé")
        form_layout.addRow("Brand (optional):", self.brand_edit)
        
        layout.addLayout(form_layout)
        
        # Info label
        info_text = {
            "Drink": "Add beverages like sodas, juices, water, energy drinks",
            "Chip": "Add potato chips, corn chips, crackers, and similar snacks",
            "Snack": "Add candy, chocolate, cookies, nuts, and other treats"
        }
        self.info_label = QLabel(info_text.get(self.category, "Add product details"))
        self.info_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        self.info_label.setWordWrap(True)
        layout.addWidget(self.info_label)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def get_values(self):
        """Get the form values."""
        return {
            'name': self.name_edit.text().strip(),
            'price': self.price_spinbox.value(),
            'brand': self.brand_edit.text().strip(),
            'category': self.category
        }

class EditProductDialog(QDialog):
    """Dialog for editing a product."""

    def __init__(self, product, parent=None):
        super().__init__(parent)
        self.product = product
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle(f"Edit {self.product['name']}")
        self.setModal(True)
        self.resize(300, 200)

        layout = QVBoxLayout(self)

        # Form layout
        form_layout = QFormLayout()

        # Product name
        self.name_edit = QLineEdit(self.product['name'])
        form_layout.addRow("Product Name:", self.name_edit)

        # Price
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.0, 1000.0)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" DZD")
        self.price_spinbox.setValue(self.product['price'])
        form_layout.addRow("Price:", self.price_spinbox)

        # Brand
        self.brand_edit = QLineEdit(self.product.get('brand', ''))
        form_layout.addRow("Brand:", self.brand_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def get_values(self):
        """Get the edited values."""
        return {
            'name': self.name_edit.text().strip(),
            'price': self.price_spinbox.value(),
            'brand': self.brand_edit.text().strip()
        }

class ProductWidget(QFrame):
    """Widget for displaying a single product."""

    def __init__(self, product, parent_panel, parent=None):
        super().__init__(parent)
        self.product = product
        self.parent_panel = parent_panel
        self.setup_ui()

    def setup_ui(self):
        """Set up the widget UI."""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMinimumSize(260, 150)
        self.setMaximumSize(310, 180)
        self.setStyleSheet("""
            ProductWidget {
                background-color: white;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                margin: 5px;
            }
            ProductWidget:hover {
                border-color: #3498db;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # Product name
        self.name_label = QLabel(self.product['name'])
        name_font = QFont()
        name_font.setPointSize(11)
        name_font.setBold(True)
        self.name_label.setFont(name_font)
        layout.addWidget(self.name_label)

        # Brand (if available)
        if self.product.get('brand'):
            self.brand_label = QLabel(f"Brand: {self.product['brand']}")
            self.brand_label.setStyleSheet("color: #7f8c8d; font-size: 9px;")
            layout.addWidget(self.brand_label)

        # Price
        self.price_label = QLabel(f"💰 {self.product['price']:.2f} DZD")
        self.price_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        layout.addWidget(self.price_label)

        # Buttons
        button_layout = QVBoxLayout()
        button_layout.setSpacing(6)

        # Top row: Edit and Delete
        top_button_layout = QHBoxLayout()

        self.edit_button = QPushButton("✏️ Edit")
        self.edit_button.setProperty("class", "info")
        self.edit_button.setMaximumHeight(28)
        self.edit_button.clicked.connect(self.edit_product)

        self.delete_button = QPushButton("🗑️ Delete")
        self.delete_button.setProperty("class", "danger")
        self.delete_button.setMaximumHeight(28)
        self.delete_button.clicked.connect(self.delete_product)

        top_button_layout.addWidget(self.edit_button)
        top_button_layout.addWidget(self.delete_button)

        # Bottom row: Sell button (full width)
        self.sell_button = QPushButton("💰 Sell")
        self.sell_button.setProperty("class", "primary")
        self.sell_button.setMaximumHeight(28)
        self.sell_button.clicked.connect(self.sell_product)

        button_layout.addLayout(top_button_layout)
        button_layout.addWidget(self.sell_button)

        layout.addLayout(button_layout)

    def sell_product(self):
        """Sell the product (record sale)."""
        # Record the sale without annoying popups
        self.parent_panel.record_sale(self.product)

        # Visual feedback - briefly change button text
        original_text = self.sell_button.text()
        self.sell_button.setText("✅ Sold!")
        self.sell_button.setEnabled(False)

        # Reset button after 1 second
        from PySide2.QtCore import QTimer
        QTimer.singleShot(1000, lambda: self.reset_sell_button(original_text))

    def reset_sell_button(self, original_text):
        """Reset the sell button to original state."""
        self.sell_button.setText(original_text)
        self.sell_button.setEnabled(True)

    def edit_product(self):
        """Edit product details."""
        dialog = EditProductDialog(self.product, self)
        if dialog.exec_() == QDialog.Accepted:
            values = dialog.get_values()
            if values['name']:
                # Update product
                self.product['name'] = values['name']
                self.product['price'] = values['price']
                self.product['brand'] = values['brand']

                # Update display
                self.update_display()

                # Update parent panel statistics
                self.parent_panel.update_display()

    def delete_product(self):
        """Delete the product."""
        reply = QMessageBox.question(
            self, 'Delete Product',
            f'Are you sure you want to delete "{self.product["name"]}"?\nThis action cannot be undone.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Remove from parent panel
            self.parent_panel.remove_product(self.product)

    def update_display(self):
        """Update the display after changes."""
        self.name_label.setText(self.product['name'])
        self.price_label.setText(f"💰 {self.product['price']:.2f} DZD")

        # Update brand label if it exists
        if hasattr(self, 'brand_label'):
            if self.product.get('brand'):
                self.brand_label.setText(f"Brand: {self.product['brand']}")
                self.brand_label.setVisible(True)
            else:
                self.brand_label.setVisible(False)

class ProductCategoryPanel(QWidget):
    """Panel for a specific product category."""
    
    def __init__(self, category, data_manager, parent=None):
        super().__init__(parent)
        self.category = category
        self.data_manager = data_manager
        self.products = []
        self.product_widgets = []
        self.sales_history = []  # Track sales
        self.setup_ui()
        self.load_products()
        
    def setup_ui(self):
        """Set up the panel UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # Header
        header_layout = QHBoxLayout()
        
        # Category title with emoji
        emojis = {"Drink": "🥤", "Chip": "🍟", "Snack": "🍫"}
        title_label = QLabel(f"{emojis.get(self.category, '📦')} {self.category}s")
        title_font = QFont("Segoe UI", 18)  # Better font and slightly bigger
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        # Add product button
        category_display = self.category.title().rstrip('s')  # Remove 's' and capitalize
        self.add_button = QPushButton(f"➕ Add {category_display}")
        self.add_button.setMinimumSize(120, 35)
        self.add_button.setProperty("class", "primary")
        self.add_button.clicked.connect(self.add_product)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.add_button)
        
        layout.addLayout(header_layout)
        
        # Statistics
        self.stats_label = QLabel("Total Products: 0")
        self.stats_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
        layout.addWidget(self.stats_label)
        
        # Products area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.products_widget = QWidget()
        self.products_layout = QGridLayout(self.products_widget)
        self.products_layout.setSpacing(10)
        self.products_layout.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        
        self.scroll_area.setWidget(self.products_widget)
        layout.addWidget(self.scroll_area)
        
        # Empty state
        category_display = self.category.title().rstrip('s')  # Remove 's' and capitalize
        self.empty_label = QLabel(f"No {self.category} added yet.\nClick 'Add {category_display}' to get started.")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.empty_label.setStyleSheet("color: #7f8c8d; font-size: 14px; font-style: italic;")
        layout.addWidget(self.empty_label)
        
        self.update_display()
        
    def add_product(self):
        """Add a new product."""
        dialog = AddProductDialog(self.category, self)
        if dialog.exec_() == QDialog.Accepted:
            values = dialog.get_values()
            
            if not values['name']:
                QMessageBox.warning(self, "Error", "Please enter a product name.")
                return
                
            # Add product to data manager and local list
            self.data_manager.add_product(self.category, values)
            self.products.append(values)
            self.add_product_widget(values)
            self.update_display()
            
    def add_product_widget(self, product):
        """Add a product widget to the grid."""
        widget = ProductWidget(product, self)
        self.product_widgets.append(widget)

        # Add to grid layout
        row = len(self.product_widgets) // 4  # 4 products per row
        col = (len(self.product_widgets) - 1) % 4
        self.products_layout.addWidget(widget, row, col)

    def remove_product(self, product):
        """Remove a product from the panel."""
        # Remove from data manager and local list
        self.data_manager.remove_product(self.category, product)
        if product in self.products:
            self.products.remove(product)

        # Find and remove the widget
        for widget in self.product_widgets[:]:
            if widget.product == product:
                self.product_widgets.remove(widget)
                self.products_layout.removeWidget(widget)
                widget.deleteLater()
                break

        # Reorganize grid
        self.reorganize_grid()
        self.update_display()

    def reorganize_grid(self):
        """Reorganize the grid layout after removing a widget."""
        # Clear layout
        for i in reversed(range(self.products_layout.count())):
            item = self.products_layout.itemAt(i)
            if item and item.widget():
                item.widget().setParent(None)

        # Re-add widgets
        for i, widget in enumerate(self.product_widgets):
            row = i // 4
            col = i % 4
            self.products_layout.addWidget(widget, row, col)

    def update_display(self):
        """Update the display with current statistics."""
        total_products = len(self.products)

        self.stats_label.setText(f"Total Products: {total_products}")

        # Show/hide empty state
        self.empty_label.setVisible(total_products == 0)
        self.scroll_area.setVisible(total_products > 0)

    def record_sale(self, product):
        """Record a product sale."""
        from datetime import datetime
        sale_record = {
            'product_name': product['name'],
            'category': product['category'],
            'price': product['price'],
            'brand': product.get('brand', ''),
            'timestamp': datetime.now(),
            'sale_id': f"sale_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{product['name'].replace(' ', '_')}"
        }
        self.sales_history.append(sale_record)

    def get_sales_data(self):
        """Get sales data for this category."""
        return {
            'category': self.category,
            'products': self.products,
            'sales_history': self.sales_history,
            'total_sales': len(self.sales_history),
            'total_revenue': sum(sale['price'] for sale in self.sales_history)
        }

    def load_products(self):
        """Load products from data manager."""
        saved_products = self.data_manager.get_products(self.category)
        for product in saved_products:
            self.products.append(product)
            self.add_product_widget(product)
        self.update_display()

class ProductsPanel(QWidget):
    """Main products panel with category tabs."""
    
    def __init__(self, data_manager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the panel UI."""
        # Set solid background
        self.setStyleSheet("""
            background-color: #f8f9fa;
            color: #2c3e50;
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: #f8f9fa;
            }
            QTabBar::tab {
                background-color: #e1e8ed;
                color: #2c3e50;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)
        
        # Create category panels
        self.drinks_panel = ProductCategoryPanel("drinks", self.data_manager)
        self.chips_panel = ProductCategoryPanel("chips", self.data_manager)
        self.snacks_panel = ProductCategoryPanel("snacks", self.data_manager)
        
        # Add tabs
        self.tab_widget.addTab(self.drinks_panel, "🥤 Drinks")
        self.tab_widget.addTab(self.chips_panel, "🍟 Chips")
        self.tab_widget.addTab(self.snacks_panel, "🍫 Snacks")
        
        layout.addWidget(self.tab_widget)

    def get_all_products_data(self):
        """Get comprehensive products data for statistics."""
        drinks_data = self.drinks_panel.get_sales_data()
        chips_data = self.chips_panel.get_sales_data()
        snacks_data = self.snacks_panel.get_sales_data()

        all_products = drinks_data['products'] + chips_data['products'] + snacks_data['products']
        all_sales = drinks_data['sales_history'] + chips_data['sales_history'] + snacks_data['sales_history']

        return {
            'total_products': len(all_products),
            'drinks_count': len(drinks_data['products']),
            'chips_count': len(chips_data['products']),
            'snacks_count': len(snacks_data['products']),
            'total_sales': len(all_sales),
            'total_revenue': sum(sale['price'] for sale in all_sales),
            'all_products': all_products,
            'all_sales': all_sales,
            'by_category': {
                'drinks': drinks_data,
                'chips': chips_data,
                'snacks': snacks_data
            }
        }
