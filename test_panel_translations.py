#!/usr/bin/env python3
"""
Test script to verify panel button translations are working correctly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PySide2.QtWidgets import QApplication
from src.utils.translation_manager import get_translation_manager, tr
from src.models.data_manager import DataManager
from src.panels.ps5_panel import PS5Panel
from src.panels.arcade_panel import ArcadePanel
from src.panels.babyfoot_panel import BabyfootPanel
from src.panels.pool_panel import PoolPanel
from src.panels.products_panel import ProductsPanel

def test_panel_translations():
    """Test that panel buttons are properly translated."""
    app = QApplication(sys.argv)
    
    # Initialize data manager
    data_manager = DataManager()
    
    # Initialize translation manager
    translation_manager = get_translation_manager()
    
    print("Testing Panel Button Translations")
    print("=" * 50)
    
    # Test PS5 Panel
    print("\n1. Testing PS5 Panel:")
    ps5_panel = PS5Panel(data_manager)
    print(f"   Create button: {ps5_panel.create_button.text()}")
    print(f"   History button: {ps5_panel.history_button.text()}")
    print(f"   Title: {ps5_panel.title_label.text()}")
    
    # Test Arcade Panel
    print("\n2. Testing Arcade Panel:")
    arcade_panel = ArcadePanel(data_manager)
    print(f"   Create button: {arcade_panel.create_button.text()}")
    print(f"   History button: {arcade_panel.history_button.text()}")
    print(f"   Title: {arcade_panel.title_label.text()}")
    
    # Test Babyfoot Panel
    print("\n3. Testing Babyfoot Panel:")
    babyfoot_panel = BabyfootPanel(data_manager)
    print(f"   Create button: {babyfoot_panel.create_button.text()}")
    print(f"   History button: {babyfoot_panel.history_button.text()}")
    print(f"   Title: {babyfoot_panel.title_label.text()}")
    
    # Test Pool Panel
    print("\n4. Testing Pool Panel:")
    pool_panel = PoolPanel(data_manager)
    print(f"   Create button: {pool_panel.create_button.text()}")
    print(f"   History button: {pool_panel.history_button.text()}")
    print(f"   Title: {pool_panel.title_label.text()}")
    
    # Test Products Panel
    print("\n5. Testing Products Panel:")
    products_panel = ProductsPanel(data_manager)
    print(f"   Tab 0 (Drinks): {products_panel.tab_widget.tabText(0)}")
    print(f"   Tab 1 (Chips): {products_panel.tab_widget.tabText(1)}")
    print(f"   Tab 2 (Snacks): {products_panel.tab_widget.tabText(2)}")
    
    print("\n" + "=" * 50)
    print("Testing Language Switch to French...")
    print("=" * 50)
    
    # Switch to French
    translation_manager.set_language('fr')
    
    # Test again with French
    print("\n1. Testing PS5 Panel (French):")
    print(f"   Create button: {ps5_panel.create_button.text()}")
    print(f"   History button: {ps5_panel.history_button.text()}")
    
    print("\n2. Testing Arcade Panel (French):")
    print(f"   Create button: {arcade_panel.create_button.text()}")
    print(f"   History button: {arcade_panel.history_button.text()}")
    
    print("\n3. Testing Babyfoot Panel (French):")
    print(f"   Create button: {babyfoot_panel.create_button.text()}")
    print(f"   History button: {babyfoot_panel.history_button.text()}")
    
    print("\n4. Testing Pool Panel (French):")
    print(f"   Create button: {pool_panel.create_button.text()}")
    print(f"   History button: {pool_panel.history_button.text()}")
    
    print("\n5. Testing Products Panel (French):")
    print(f"   Tab 0 (Drinks): {products_panel.tab_widget.tabText(0)}")
    print(f"   Tab 1 (Chips): {products_panel.tab_widget.tabText(1)}")
    print(f"   Tab 2 (Snacks): {products_panel.tab_widget.tabText(2)}")
    
    print("\n" + "=" * 50)
    print("Panel Translation Test Complete!")
    print("=" * 50)

if __name__ == "__main__":
    test_panel_translations()
