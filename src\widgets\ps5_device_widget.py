"""
PS5 device widget with game mode support.
"""

from PySide2.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QLabel, QFrame, QSpinBox, QDoubleSpinBox,
                               QMessageBox, QDialog, QFormLayout, QLineEdit,
                               QDialogButtonBox, QGridLayout, QCheckBox)
from PySide2.QtCore import Qt, QTimer, Signal
from PySide2.QtGui import QFont, QPalette, QPixmap
from datetime import datetime, timedelta
from datetime import datetime, timedelta
import os

from ..models.device import Device, SessionState, PricingMode
from .device_widget import EditDeviceDialog
from ..utils.notifications import show_time_up_notification

class SessionTypeDialog(QDialog):
    """Dialog for choosing session type (open or timed)."""

    def __init__(self, device, parent=None):
        super().__init__(parent)
        self.device = device
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle(f"Start Session - {self.device.name}")
        self.setModal(True)
        self.resize(350, 250)

        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("Choose Session Type")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Session type buttons
        self.open_session_button = QPushButton("🔓 Open Session")
        self.open_session_button.setMinimumHeight(50)
        self.open_session_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        self.open_session_button.clicked.connect(self.choose_open_session)
        layout.addWidget(self.open_session_button)

        open_info = QLabel("Play without time limit - pay by actual time used")
        open_info.setStyleSheet("color: #7f8c8d; font-style: italic; margin-bottom: 10px;")
        open_info.setAlignment(Qt.AlignCenter)
        layout.addWidget(open_info)

        self.timed_session_button = QPushButton("⏰ Timed Session")
        self.timed_session_button.setMinimumHeight(50)
        self.timed_session_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #5dade2;
            }
        """)
        self.timed_session_button.clicked.connect(self.choose_timed_session)
        layout.addWidget(self.timed_session_button)

        timed_info = QLabel("Set specific time - get notification when time is up")
        timed_info.setStyleSheet("color: #7f8c8d; font-style: italic;")
        timed_info.setAlignment(Qt.AlignCenter)
        layout.addWidget(timed_info)

        # Cancel button
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        layout.addWidget(cancel_button)

        self.session_type = None
        self.booked_minutes = None

    def choose_open_session(self):
        """Choose open session."""
        self.session_type = "open"
        self.booked_minutes = None
        self.accept()

    def choose_timed_session(self):
        """Choose timed session and show time booking."""
        dialog = TimeBookingDialog(self.device, self)
        if dialog.exec_() == QDialog.Accepted:
            booked_time = dialog.get_booking_time()
            if booked_time > 0:
                self.session_type = "timed"
                self.booked_minutes = booked_time
                self.accept()
            else:
                QMessageBox.warning(self, "Error", "Please select a valid time.")

    def get_session_config(self):
        """Get session configuration."""
        return {
            'type': self.session_type,
            'booked_minutes': self.booked_minutes
        }

class TimeBookingDialog(QDialog):
    """Dialog for booking specific session time."""

    def __init__(self, device, parent=None):
        super().__init__(parent)
        self.device = device
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle(f"Set Time - {self.device.name}")
        self.setModal(True)
        self.resize(300, 200)

        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("How long do you want to play?")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Time selection
        form_layout = QFormLayout()

        # Hours
        self.hours_spinbox = QSpinBox()
        self.hours_spinbox.setRange(0, 12)
        self.hours_spinbox.setValue(0)
        self.hours_spinbox.setSuffix(" hours")
        form_layout.addRow("Hours:", self.hours_spinbox)

        # Minutes
        self.minutes_spinbox = QSpinBox()
        self.minutes_spinbox.setRange(1, 59)  # Minimum 1 minute
        self.minutes_spinbox.setValue(30)  # Default 30 minutes
        self.minutes_spinbox.setSuffix(" minutes")
        form_layout.addRow("Minutes:", self.minutes_spinbox)

        layout.addLayout(form_layout)

        # Cost preview
        self.cost_label = QLabel()
        self.update_cost_preview()
        layout.addWidget(self.cost_label)

        # Connect spinboxes to update cost
        self.hours_spinbox.valueChanged.connect(self.update_cost_preview)
        self.minutes_spinbox.valueChanged.connect(self.update_cost_preview)

        # Info
        info_label = QLabel("🔔 You'll get a Windows notification with sound when time is up!")
        info_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def update_cost_preview(self):
        """Update the cost preview."""
        total_minutes = self.hours_spinbox.value() * 60 + self.minutes_spinbox.value()
        total_hours = total_minutes / 60.0
        cost = total_hours * self.device.price_per_unit
        self.cost_label.setText(f"💰 Estimated cost: {cost:.2f} DZD")

    def get_booking_time(self):
        """Get the booked time in minutes."""
        return self.hours_spinbox.value() * 60 + self.minutes_spinbox.value()

class PS5DeviceWidget(QWidget):
    """Widget for displaying and controlling a PS5 device with game modes."""
    
    device_deleted = Signal(str)  # Emitted when device is deleted
    device_updated = Signal()     # Emitted when device is updated
    
    def __init__(self, device: Device, parent=None):
        super().__init__(parent)
        self.device = device
        self.setup_ui()
        self.setup_timer()
        
    def setup_ui(self):
        """Set up the widget UI."""
        # Main frame
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMinimumSize(290, 280)
        self.setMaximumSize(330, 320)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 6, 6, 6)
        layout.setSpacing(4)
        
        # Device image
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMaximumHeight(50)
        self.image_label.setMinimumHeight(50)
        self.setup_device_image()
        layout.addWidget(self.image_label)
        
        # Header with device name and controls
        header_layout = QHBoxLayout()
        
        self.name_label = QLabel(self.device.name)
        name_font = QFont()
        name_font.setPointSize(12)
        name_font.setBold(True)
        self.name_label.setFont(name_font)
        
        # Edit and delete buttons
        self.edit_button = QPushButton("✏️")
        self.edit_button.setMaximumSize(30, 30)
        self.edit_button.clicked.connect(self.edit_device)
        
        self.delete_button = QPushButton("🗑️")
        self.delete_button.setMaximumSize(30, 30)
        self.delete_button.clicked.connect(self.delete_device)
        
        header_layout.addWidget(self.name_label)
        header_layout.addStretch()
        header_layout.addWidget(self.edit_button)
        header_layout.addWidget(self.delete_button)
        
        layout.addLayout(header_layout)
        
        # Status and pricing info
        self.status_label = QLabel()
        self.price_label = QLabel()
        self.timer_label = QLabel()
        self.cost_label = QLabel()
        
        layout.addWidget(self.status_label)
        layout.addWidget(self.price_label)
        layout.addWidget(self.timer_label)
        layout.addWidget(self.cost_label)
        
        # Control buttons - compact layout
        button_widget = QWidget()
        button_grid = QGridLayout(button_widget)
        button_grid.setSpacing(2)
        button_grid.setContentsMargins(0, 0, 0, 0)
        
        self.start_button = QPushButton("Start")
        self.start_button.setProperty("class", "primary")
        self.start_button.setMaximumHeight(22)
        self.start_button.setMinimumHeight(22)
        self.start_button.clicked.connect(self.start_session)
        
        self.pause_button = QPushButton("Pause")
        self.pause_button.setProperty("class", "warning")
        self.pause_button.setMaximumHeight(22)
        self.pause_button.setMinimumHeight(22)
        self.pause_button.clicked.connect(self.pause_session)
        
        self.end_button = QPushButton("End")
        self.end_button.setProperty("class", "danger")
        self.end_button.setMaximumHeight(22)
        self.end_button.setMinimumHeight(22)
        self.end_button.clicked.connect(self.end_session)
        
        # Arrange buttons in a single row
        button_grid.addWidget(self.start_button, 0, 0)
        button_grid.addWidget(self.pause_button, 0, 1)
        button_grid.addWidget(self.end_button, 0, 2)

        layout.addWidget(button_widget)

        # Game mode checkbox
        self.game_mode_checkbox = QCheckBox("Game Mode")
        self.game_mode_checkbox.setMaximumHeight(22)
        self.game_mode_checkbox.toggled.connect(self.toggle_game_mode)
        layout.addWidget(self.game_mode_checkbox)

        # Game mode buttons row (initially hidden)
        self.game_widget = QWidget()
        game_layout = QHBoxLayout(self.game_widget)
        game_layout.setSpacing(2)
        game_layout.setContentsMargins(0, 0, 0, 0)

        self.game_1v1_button = QPushButton("1v1")
        self.game_1v1_button.setProperty("class", "primary")
        self.game_1v1_button.setMaximumHeight(22)
        self.game_1v1_button.setMinimumHeight(22)
        self.game_1v1_button.clicked.connect(lambda: self.start_game_mode("1v1"))

        self.game_2v2_button = QPushButton("2v2")
        self.game_2v2_button.setProperty("class", "primary")
        self.game_2v2_button.setMaximumHeight(22)
        self.game_2v2_button.setMinimumHeight(22)
        self.game_2v2_button.clicked.connect(lambda: self.start_game_mode("2v2"))
        # Hide 2v2 for VIP PS5
        if self.device.device_type.value != "ps5_regular":
            self.game_2v2_button.setVisible(False)

        self.add_game_button = QPushButton("+Game")
        self.add_game_button.setProperty("class", "info")
        self.add_game_button.setMaximumHeight(22)
        self.add_game_button.setMinimumHeight(22)
        self.add_game_button.clicked.connect(self.add_game)
        self.add_game_button.setVisible(False)

        self.end_game_button = QPushButton("End Game")
        self.end_game_button.setProperty("class", "danger")
        self.end_game_button.setMaximumHeight(22)
        self.end_game_button.setMinimumHeight(22)
        self.end_game_button.clicked.connect(self.end_game_session)
        self.end_game_button.setVisible(False)

        game_layout.addWidget(self.game_1v1_button)
        game_layout.addWidget(self.game_2v2_button)
        game_layout.addWidget(self.add_game_button)
        game_layout.addWidget(self.end_game_button)

        # Initially hide the game widget
        self.game_widget.setVisible(False)
        layout.addWidget(self.game_widget)
        
        # Update display
        self.update_display()
        
    def setup_device_image(self):
        """Set up the device image based on device type."""
        # Get the directory where this file is located
        current_dir = os.path.dirname(os.path.dirname(__file__))
        images_dir = os.path.join(current_dir, "images")
        
        # Choose image based on device type
        if self.device.device_type.value == "ps5_vip":
            image_file = "ps5pro.png"
        else:
            image_file = "ps5.png"
            
        image_path = os.path.join(images_dir, image_file)
        
        if os.path.exists(image_path):
            pixmap = QPixmap(image_path)
            # Scale the image to fit the label while maintaining aspect ratio
            scaled_pixmap = pixmap.scaled(45, 45, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.image_label.setPixmap(scaled_pixmap)
        else:
            # Fallback to text if image not found
            self.image_label.setText(f"🎮\n{self.device.device_type.value.replace('_', ' ').title()}")
            self.image_label.setStyleSheet("color: #3498db; font-weight: bold; font-size: 10px;")
        
    def setup_timer(self):
        """Set up timer for regular updates."""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # Update every second
        
    def update_display(self):
        """Update the display with current device state."""
        # Status
        if self.device.current_session:
            state = self.device.current_session.state
            if state == SessionState.ACTIVE:
                self.status_label.setText("🟢 Active")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
            elif state == SessionState.PAUSED:
                self.status_label.setText("🟡 Paused")
                self.status_label.setStyleSheet("color: orange; font-weight: bold;")
        else:
            self.status_label.setText("⚪ Idle")
            self.status_label.setStyleSheet("color: gray; font-weight: bold;")
            
        # Pricing info
        self.price_label.setText(f"💰 {self.device.price_per_unit:.2f} DZD/hour")
        
        # Timer and cost
        if self.device.current_session:
            session = self.device.current_session
            
            # Always show timer, but add game info for game modes
            duration = session.get_duration()
            hours = int(duration.total_seconds() // 3600)
            minutes = int((duration.total_seconds() % 3600) // 60)
            seconds = int(duration.total_seconds() % 60)

            if session.pricing_mode == PricingMode.HOURLY:
                timer_text = f"⏱️ {hours:02d}:{minutes:02d}:{seconds:02d}"
                # Add remaining time if booked
                if session.booked_minutes:
                    remaining = session.get_remaining_time()
                    remaining_minutes = int(remaining.total_seconds() // 60)
                    remaining_seconds = int(remaining.total_seconds() % 60)
                    if remaining_minutes > 0 or remaining_seconds > 0:
                        timer_text += f" | ⏰ -{remaining_minutes:02d}:{remaining_seconds:02d}"
                    else:
                        timer_text += " | ⏰ TIME UP!"
                self.timer_label.setText(timer_text)
            else:  # Game modes - show both timer and games
                games = session.games_played
                mode = session.game_mode or "Game"
                self.timer_label.setText(f"⏱️ {hours:02d}:{minutes:02d}:{seconds:02d} | 🎮 {mode}: {games} games")
                
            cost = self.device.get_current_cost()
            self.cost_label.setText(f"💵 Current: {cost:.2f} DZD")
        else:
            self.timer_label.setText("⏱️ --:--:--")
            self.cost_label.setText("💵 Current: 0.00 DZD")
            
        # Button states
        has_session = self.device.current_session is not None
        is_active = has_session and self.device.current_session.state == SessionState.ACTIVE
        is_paused = has_session and self.device.current_session.state == SessionState.PAUSED
        is_game_mode = has_session and self.device.current_session.pricing_mode in [PricingMode.PS5_1V1, PricingMode.PS5_2V2]
        is_hourly_mode = has_session and self.device.current_session.pricing_mode == PricingMode.HOURLY

        # Regular session buttons
        self.start_button.setEnabled(not has_session)
        self.pause_button.setEnabled((is_active or is_paused) and is_hourly_mode)  # Only for hourly sessions
        self.end_button.setEnabled(has_session and is_hourly_mode)  # Only for hourly sessions

        # Game mode controls
        self.game_mode_checkbox.setEnabled(not has_session)
        self.game_mode_checkbox.setVisible(not has_session)

        # Game mode buttons visibility
        if has_session and is_game_mode:
            # Show game controls when in game mode
            self.game_widget.setVisible(True)
            self.game_1v1_button.setVisible(False)
            self.game_2v2_button.setVisible(False)
            self.add_game_button.setVisible(True)
            self.end_game_button.setVisible(True)
        elif not has_session and self.game_mode_checkbox.isChecked():
            # Show game mode selection when checkbox is checked
            self.game_widget.setVisible(True)
            self.game_1v1_button.setVisible(True)
            if self.device.device_type.value == "ps5_regular":
                self.game_2v2_button.setVisible(True)
            self.add_game_button.setVisible(False)
            self.end_game_button.setVisible(False)
        else:
            # Hide game widget
            self.game_widget.setVisible(False)

        # Update pause button text
        if is_paused:
            self.pause_button.setText("Resume")
        else:
            self.pause_button.setText("Pause")

        # Check for time up notification
        if (has_session and is_hourly_mode and
            self.device.current_session.is_time_up() and
            not hasattr(self, '_time_up_notified')):
            self._time_up_notified = True
            self.show_time_up_notification()
            
    def start_session(self):
        """Start a new hourly session (open or timed)."""
        try:
            # Show session type dialog
            dialog = SessionTypeDialog(self.device, self)
            if dialog.exec_() == QDialog.Accepted:
                config = dialog.get_session_config()
                if config['type'] == 'open':
                    self.device.start_session()  # Open session
                elif config['type'] == 'timed':
                    self.device.start_session(booked_minutes=config['booked_minutes'])  # Timed session
                self.device_updated.emit()
        except ValueError as e:
            QMessageBox.warning(self, "Error", str(e))

    def toggle_game_mode(self, checked):
        """Toggle game mode visibility."""
        if checked and not self.device.current_session:
            self.game_widget.setVisible(True)
            self.game_1v1_button.setVisible(True)
            if self.device.device_type.value == "ps5_regular":
                self.game_2v2_button.setVisible(True)
            self.add_game_button.setVisible(False)
            self.end_game_button.setVisible(False)
        else:
            self.game_widget.setVisible(False)

    def start_game_mode(self, mode):
        """Start a game session in the specified mode."""
        try:
            # Use stored prices for game modes
            if mode == "1v1":
                price = getattr(self.device, 'game_1v1_price', 200.0 if self.device.device_type.value == "ps5_vip" else 100.0)
            else:  # 2v2
                price = getattr(self.device, 'game_2v2_price', 150.0)

            self.device.start_ps5_game_session(mode, price)
            # Automatically add the first game
            self.device.add_game()
            self.device_updated.emit()

            # Uncheck the checkbox and update display
            self.game_mode_checkbox.setChecked(False)

        except ValueError as e:
            QMessageBox.warning(self, "Error", str(e))

    def end_game_session(self):
        """End the current game session."""
        if self.device.current_session:
            games = self.device.current_session.games_played
            cost = self.device.get_current_cost()
            mode = self.device.current_session.game_mode or "Game"

            reply = QMessageBox.question(
                self, 'End Game Session',
                f'End {mode} session for {self.device.name}?\n'
                f'Games played: {games}\n'
                f'Total cost: {cost:.2f} DZD',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.device.end_session()
                # Reset notification flag
                if hasattr(self, '_time_up_notified'):
                    delattr(self, '_time_up_notified')
                self.device_updated.emit()

    def show_time_up_notification(self):
        """Show Windows notification when booked time is up."""
        if self.device.current_session and self.device.current_session.booked_minutes:
            booked_time = self.device.current_session.booked_minutes
            current_cost = self.device.get_current_cost()

            # Try Windows toast notification first
            toast_success = show_time_up_notification(
                self.device.name,
                booked_time,
                current_cost
            )

            # Always show dialog as well for interaction
            hours = booked_time // 60
            minutes = booked_time % 60

            time_str = ""
            if hours > 0:
                time_str = f"{hours} hour{'s' if hours > 1 else ''}"
                if minutes > 0:
                    time_str += f" and {minutes} minute{'s' if minutes > 1 else ''}"
            else:
                time_str = f"{minutes} minute{'s' if minutes > 1 else ''}"

            # Show dialog for user interaction
            reply = QMessageBox.question(
                self, '⏰ Time Up!',
                f'The booked time ({time_str}) for {self.device.name} has elapsed!\n\n'
                f'Current cost: {current_cost:.2f} DZD\n\n'
                f'Do you want to end the session now?',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.device.end_session()
                if hasattr(self, '_time_up_notified'):
                    delattr(self, '_time_up_notified')
                self.device_updated.emit()

    def add_game(self):
        """Add another game to the current game session."""
        if self.device.current_session:
            self.device.add_game()
            self.device_updated.emit()

    def pause_session(self):
        """Pause or resume the current session."""
        if self.device.current_session:
            current_state = self.device.current_session.state
            
            if current_state == SessionState.ACTIVE:
                self.device.pause_session()
            elif current_state == SessionState.PAUSED:
                self.device.resume_session()
            
            # Emit signal and force immediate display update
            self.device_updated.emit()
            self.update_display()
            
    def end_session(self):
        """End the current session."""
        if self.device.current_session:
            cost = self.device.get_current_cost()
            reply = QMessageBox.question(
                self, 'End Session',
                f'End session for {self.device.name}?\nTotal cost: {cost:.2f} DZD',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                self.device.end_session()
                # Reset notification flag
                if hasattr(self, '_time_up_notified'):
                    delattr(self, '_time_up_notified')
                self.device_updated.emit()
                

            
    def edit_device(self):
        """Edit device properties."""
        dialog = EditDeviceDialog(self.device, self)
        if dialog.exec_() == QDialog.Accepted:
            values = dialog.get_values()
            if values['name']:
                self.device.name = values['name']
                self.device.price_per_unit = values['price_per_unit']

                # Update PS5 game pricing if applicable
                if 'game_1v1_price' in values:
                    self.device.game_1v1_price = values['game_1v1_price']
                if 'game_2v2_price' in values:
                    self.device.game_2v2_price = values['game_2v2_price']

                self.name_label.setText(self.device.name)
                self.device_updated.emit()
                
    def delete_device(self):
        """Delete the device."""
        reply = QMessageBox.question(
            self, 'Delete Device',
            f'Are you sure you want to delete "{self.device.name}"?\nThis action cannot be undone.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.device.current_session:
                # End current session before deleting
                self.device.end_session()
            self.device.is_active = False
            self.device_deleted.emit(self.device.id)
            
    def setFrameStyle(self, style):
        """Set frame style for the widget."""
        self.setStyleSheet("""
            PS5DeviceWidget {
                background-color: white;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                margin: 5px;
            }
            
            PS5DeviceWidget:hover {
                border-color: #2c3e50;
            }
        """)
