#!/bin/bash

echo "========================================"
echo "    Ωmega GameZone - Build Script"
echo "========================================"
echo

echo "[1/5] Installing required packages..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install dependencies"
    exit 1
fi

echo
echo "[2/5] Cleaning previous builds..."
rm -rf dist build

echo
echo "[3/5] Building executable with PyInstaller..."
pyinstaller omega_gamezone.spec --clean
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to build executable"
    exit 1
fi

echo
echo "[4/5] Copying additional files..."
cp README.md "dist/Omega GameZone/" 2>/dev/null || true
cp requirements.txt "dist/Omega GameZone/" 2>/dev/null || true

echo
echo "[5/5] Creating release package..."
cd dist
tar -czf "../Omega_GameZone_Release.tar.gz" "Omega GameZone"
cd ..

echo
echo "========================================"
echo "           BUILD COMPLETE!"
echo "========================================"
echo
echo "Executable location: dist/Omega GameZone/Omega GameZone"
echo "Release package: Omega_GameZone_Release.tar.gz"
echo
echo "The application includes:"
echo "- Main executable"
echo "- All images and assets"
echo "- AI libraries (PuLP, Experta, PyOD)"
echo "- Data persistence system"
echo "- Complete UI with advanced features"
echo
