"""
Configuration manager for handling application settings.
"""

import json
import os
from typing import Dict, Any, Optional

class ConfigManager:
    """Manages application configuration settings."""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config: Dict[str, Any] = {}
        self.default_config = {
            "app_settings": {
                "shop_name": "Ωmega GameZone",
                "version": "1.0.0",
                "theme": "default",
                "language": "en"
            },
            "display_settings": {
                "window_width": 1400,
                "window_height": 900,
                "min_width": 1200,
                "min_height": 800
            },
            "business_settings": {
                "currency": "DZD",
                "tax_rate": 0.0,
                "auto_save_interval": 60
            }
        }
        self.load_config()
    
    def load_config(self):
        """Load configuration from file."""
        if not os.path.exists(self.config_file):
            self.config = self.default_config.copy()
            self.save_config()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            # Merge with defaults to ensure all keys exist
            self._merge_with_defaults()
            
        except Exception as e:
            print(f"Error loading config: {e}")
            self.config = self.default_config.copy()
            self.save_config()
    
    def save_config(self):
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def _merge_with_defaults(self):
        """Merge current config with defaults to ensure all keys exist."""
        def merge_dict(default: dict, current: dict) -> dict:
            result = default.copy()
            for key, value in current.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        self.config = merge_dict(self.default_config, self.config)
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        return self.config.get(section, {}).get(key, default)
    
    def set(self, section: str, key: str, value: Any):
        """Set a configuration value."""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
        self.save_config()
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """Get an entire configuration section."""
        return self.config.get(section, {})
    
    def set_section(self, section: str, values: Dict[str, Any]):
        """Set an entire configuration section."""
        self.config[section] = values
        self.save_config()
    
    def get_shop_name(self) -> str:
        """Get the shop name."""
        return self.get("app_settings", "shop_name", "Ωmega GameZone")
    
    def set_shop_name(self, name: str):
        """Set the shop name."""
        self.set("app_settings", "shop_name", name)
    
    def get_window_size(self) -> tuple:
        """Get the window size."""
        width = self.get("display_settings", "window_width", 1400)
        height = self.get("display_settings", "window_height", 900)
        return (width, height)
    
    def get_min_window_size(self) -> tuple:
        """Get the minimum window size."""
        width = self.get("display_settings", "min_width", 1200)
        height = self.get("display_settings", "min_height", 800)
        return (width, height)
    
    def reset_to_defaults(self):
        """Reset configuration to default values."""
        self.config = self.default_config.copy()
        self.save_config()