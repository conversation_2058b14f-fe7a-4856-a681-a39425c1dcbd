@echo off
echo ========================================
echo    Ωmega GameZone - Build Script
echo ========================================
echo.

echo [1/5] Installing required packages...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [2/5] Cleaning previous builds...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo.
echo [3/5] Building executable with PyInstaller...
pyinstaller omega_gamezone.spec --clean
if %errorlevel% neq 0 (
    echo ERROR: Failed to build executable
    pause
    exit /b 1
)

echo.
echo [4/5] Copying additional files...
copy "README.md" "dist\Omega GameZone\" 2>nul
copy "requirements.txt" "dist\Omega GameZone\" 2>nul

echo.
echo [5/5] Creating release package...
if exist "Omega_GameZone_Release.zip" del "Omega_GameZone_Release.zip"
powershell -command "Compress-Archive -Path 'dist\Omega GameZone\*' -DestinationPath 'Omega_GameZone_Release.zip'"

echo.
echo ========================================
echo           BUILD COMPLETE!
echo ========================================
echo.
echo Executable location: dist\Omega GameZone\Omega GameZone.exe
echo Release package: Omega_GameZone_Release.zip
echo.
echo The application includes:
echo - Main executable
echo - All images and assets
echo - AI libraries (PuLP, Experta, PyOD)
echo - Data persistence system
echo - Complete UI with advanced features
echo.
pause
