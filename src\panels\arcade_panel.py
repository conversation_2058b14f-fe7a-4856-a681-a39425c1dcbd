"""
Arcade panel for managing arcade gaming machines.
"""

from PySide2.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QLabel, QScrollArea, QFrame, QGridLayout,
                               QDialog, QFormLayout, QLineEdit, QDoubleSpinBox,
                               QDialogButtonBox, QMessageBox)
from PySide2.QtCore import Qt
from PySide2.QtGui import QFont, QPixmap
import os

from ..models.device import Device, DeviceType
from ..models.data_manager import DataManager
from ..widgets.device_widget import DeviceWidget
from .history_panel import HistoryPanel
from ..utils.translation_manager import get_translation_manager, tr

class CreateArcadeDialog(QDialog):
    """Dialog for creating a new arcade machine."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Create Arcade Machine")
        self.setModal(True)
        self.resize(350, 200)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Machine name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("e.g., Street Fighter Cabinet")
        form_layout.addRow("Machine Name:", self.name_edit)
        
        # Price per hour
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.0, 10000.0)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" DZD")
        self.price_spinbox.setValue(200.0)  # Default arcade price
        form_layout.addRow("Price per Hour:", self.price_spinbox)
        
        layout.addLayout(form_layout)
        
        # Info label
        self.info_label = QLabel("Arcade machines are charged by the hour.\nTimer starts when session begins.")
        self.info_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        self.info_label.setWordWrap(True)
        layout.addWidget(self.info_label)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def get_values(self):
        """Get the form values."""
        return {
            'name': self.name_edit.text().strip(),
            'price_per_hour': self.price_spinbox.value()
        }

class ArcadePanel(QWidget):
    """Panel for managing arcade gaming machines."""
    
    def __init__(self, data_manager: DataManager):
        super().__init__()
        self.data_manager = data_manager
        self.device_widgets = {}
        self.translation_manager = get_translation_manager()
        self.translation_manager.language_changed.connect(self.update_texts)
        self.setup_ui()
        self.load_devices()
        
    def setup_ui(self):
        """Set up the panel UI."""
        # Set solid background for the panel with proper text styling
        self.setStyleSheet("""
            background-color: #f8f9fa;
            color: #2c3e50;
            QLabel {
                color: #2c3e50;
                background-color: transparent;
            }
            QPushButton {
                color: white;
                background-color: #3498db;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # Header
        header_layout = QHBoxLayout()

        # Header with image and title
        header_content = QHBoxLayout()

        # Arcade image
        arcade_image_label = QLabel()
        current_dir = os.path.dirname(os.path.dirname(__file__))
        arcade_image_path = os.path.join(current_dir, "images", "arcade.png")
        if os.path.exists(arcade_image_path):
            pixmap = QPixmap(arcade_image_path)
            scaled_pixmap = pixmap.scaled(65, 65, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            arcade_image_label.setPixmap(scaled_pixmap)
        else:
            arcade_image_label.setText("🕹️")
            arcade_image_label.setStyleSheet("font-size: 45px;")

        self.title_label = QLabel()
        title_font = QFont("Segoe UI", 20)  # Better font and slightly bigger
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet("color: black; margin-left: 10px;")

        header_content.addWidget(arcade_image_label)
        header_content.addWidget(self.title_label)
        header_content.addStretch()

        # Control buttons
        self.create_button = QPushButton()
        self.create_button.setMinimumSize(150, 40)
        self.create_button.setProperty("class", "primary")
        self.create_button.clicked.connect(self.create_machine)

        self.history_button = QPushButton()
        self.history_button.setMinimumSize(120, 40)
        self.history_button.setProperty("class", "info")
        self.history_button.clicked.connect(self.show_history)

        # Update button texts
        self.update_texts()
        
        header_layout.addLayout(header_content)
        header_layout.addWidget(self.create_button)
        header_layout.addWidget(self.history_button)
        
        layout.addLayout(header_layout)
        
        # Statistics summary
        self.stats_frame = QFrame()
        self.stats_frame.setFrameStyle(QFrame.StyledPanel)
        self.stats_frame.setMaximumHeight(80)
        stats_layout = QHBoxLayout(self.stats_frame)
        
        self.total_machines_label = QLabel("Total Machines: 0")
        self.active_sessions_label = QLabel("Active Sessions: 0")
        self.total_revenue_label = QLabel("Total Revenue: 0.00 DZD")
        
        stats_layout.addWidget(self.total_machines_label)
        stats_layout.addWidget(self.active_sessions_label)
        stats_layout.addWidget(self.total_revenue_label)
        stats_layout.addStretch()
        
        layout.addWidget(self.stats_frame)
        
        # Devices area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.devices_widget = QWidget()
        self.devices_layout = QGridLayout(self.devices_widget)
        self.devices_layout.setSpacing(15)
        self.devices_layout.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        
        self.scroll_area.setWidget(self.devices_widget)
        layout.addWidget(self.scroll_area)
        
        # Empty state message
        self.empty_label = QLabel("No arcade machines created yet.\nClick 'Create Machine' to add your first arcade cabinet.")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.empty_label.setStyleSheet("color: #7f8c8d; font-size: 14px; font-style: italic;")
        self.empty_label.setVisible(True)
        layout.addWidget(self.empty_label)
        
    def load_devices(self):
        """Load arcade devices from data manager."""
        arcade_devices = self.data_manager.get_devices_by_type(DeviceType.ARCADE)
                     
        for device in arcade_devices:
            self.add_device_widget(device)
            
        self.update_display()
        
    def create_machine(self):
        """Create a new arcade machine."""
        dialog = CreateArcadeDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            values = dialog.get_values()
            
            if not values['name']:
                QMessageBox.warning(self, "Error", "Please enter a machine name.")
                return
                
            # Check for duplicate names
            existing_names = [device.name for device in self.data_manager.get_all_devices()]
            if values['name'] in existing_names:
                QMessageBox.warning(self, "Error", "A device with this name already exists.")
                return
                
            # Create device
            device = Device(values['name'], DeviceType.ARCADE, values['price_per_hour'])
            
            # Add to data manager
            self.data_manager.add_device(device)
            
            # Add to UI
            self.add_device_widget(device)
            self.update_display()
            
    def add_device_widget(self, device: Device):
        """Add a device widget to the panel."""
        device_widget = DeviceWidget(device)
        device_widget.device_deleted.connect(self.remove_device_widget)
        device_widget.device_updated.connect(self.on_device_updated)
        
        self.device_widgets[device.id] = device_widget
        
        # Add to grid layout
        row = len(self.device_widgets) // 3
        col = (len(self.device_widgets) - 1) % 3
        self.devices_layout.addWidget(device_widget, row, col)
        
    def remove_device_widget(self, device_id: str):
        """Remove a device widget from the panel."""
        if device_id in self.device_widgets:
            widget = self.device_widgets[device_id]
            self.devices_layout.removeWidget(widget)
            widget.deleteLater()
            del self.device_widgets[device_id]
            
            # Remove from data manager
            self.data_manager.remove_device(device_id)
            
            # Reorganize grid
            self.reorganize_grid()
            self.update_display()
            
    def reorganize_grid(self):
        """Reorganize the grid layout after removing a widget."""
        # Clear layout
        for i in reversed(range(self.devices_layout.count())):
            self.devices_layout.itemAt(i).widget().setParent(None)
            
        # Re-add widgets
        for i, widget in enumerate(self.device_widgets.values()):
            row = i // 3
            col = i % 3
            self.devices_layout.addWidget(widget, row, col)
            
    def update_display(self):
        """Update the display with current statistics."""
        arcade_devices = self.data_manager.get_devices_by_type(DeviceType.ARCADE)
                     
        total_machines = len(arcade_devices)
        active_sessions = sum(1 for device in arcade_devices if device.current_session)
        total_revenue = sum(device.get_total_revenue() for device in arcade_devices)
        
        self.total_machines_label.setText(f"Total Machines: {total_machines}")
        self.active_sessions_label.setText(f"Active Sessions: {active_sessions}")
        self.total_revenue_label.setText(f"Total Revenue: {total_revenue:.2f} DZD")
        
        # Show/hide empty state
        self.empty_label.setVisible(total_machines == 0)
        self.scroll_area.setVisible(total_machines > 0)

    def update_texts(self):
        """Update all translatable texts."""
        self.title_label.setText("Arcade Machines")  # Keep English for now
        self.create_button.setText(f"➕ {tr('buttons.add')} Machine")
        self.history_button.setText(f"📋 {tr('history.title')}")

    def show_history(self):
        """Show history for arcade devices."""
        arcade_devices = self.data_manager.get_devices_by_type(DeviceType.ARCADE)
                     
        if not arcade_devices:
            QMessageBox.information(self, "No History", "No arcade machines found.")
            return
            
        history_panel = HistoryPanel(arcade_devices, "Arcade Machines History", self)
        history_panel.exec_()

    def on_device_updated(self):
        """Handle device updates by saving data and refreshing display."""
        self.data_manager.save_data()
        self.update_display()
