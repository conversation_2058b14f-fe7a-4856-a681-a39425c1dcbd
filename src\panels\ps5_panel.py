"""
PS5 panel for managing PS5 gaming stations.
"""

from PySide2.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QLabel, QScrollArea, QFrame, QGridLayout,
                               QDialog, QFormLayout, QLineEdit, QDoubleSpinBox,
                               QComboBox, QDialogButtonBox, QMessageBox, QRadioButton,
                               QButtonGroup)
from PySide2.QtCore import Qt
from PySide2.QtGui import QFont, QPixmap
import os

from ..models.device import Device, DeviceType
from ..models.data_manager import DataManager
from ..widgets.ps5_device_widget import PS5DeviceWidget
from .history_panel import HistoryPanel

class CreatePS5Dialog(QDialog):
    """Dialog for creating a new PS5 station."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Create PS5 Station")
        self.setModal(True)
        self.resize(350, 250)
        
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        # Station name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("e.g., PS5 Station 1")
        form_layout.addRow("Station Name:", self.name_edit)
        
        # Station type
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Regular", "VIP"])
        self.type_combo.currentTextChanged.connect(self.update_price_suggestion)
        form_layout.addRow("Station Type:", self.type_combo)
        
        # Price per hour
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.0, 10000.0)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" DZD")
        self.price_spinbox.setValue(300.0)  # Default regular price
        form_layout.addRow("Price per Hour:", self.price_spinbox)
        
        layout.addLayout(form_layout)
        
        # Info label
        self.info_label = QLabel("Regular stations are for standard gaming.\nVIP stations offer premium experience.")
        self.info_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        self.info_label.setWordWrap(True)
        layout.addWidget(self.info_label)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Set initial price suggestion
        self.update_price_suggestion()
        
    def update_price_suggestion(self):
        """Update price suggestion based on station type."""
        if self.type_combo.currentText() == "VIP":
            self.price_spinbox.setValue(500.0)
        else:
            self.price_spinbox.setValue(300.0)
            
    def get_values(self):
        """Get the form values."""
        return {
            'name': self.name_edit.text().strip(),
            'is_vip': self.type_combo.currentText() == "VIP",
            'price_per_hour': self.price_spinbox.value()
        }

class PS5SessionDialog(QDialog):
    """Dialog for choosing PS5 session mode."""

    def __init__(self, device, parent=None):
        super().__init__(parent)
        self.device = device
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle(f"Start Session - {self.device.name}")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # Title
        title_label = QLabel("Choose Session Mode")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # Session mode selection
        self.mode_group = QButtonGroup()

        # Hourly mode
        self.hourly_radio = QRadioButton("Hourly Session")
        self.hourly_radio.setChecked(True)
        self.mode_group.addButton(self.hourly_radio, 0)
        layout.addWidget(self.hourly_radio)

        hourly_info = QLabel(f"Play by time - {self.device.price_per_unit:.0f} DZD/hour")
        hourly_info.setStyleSheet("color: #7f8c8d; margin-left: 20px; margin-bottom: 10px;")
        layout.addWidget(hourly_info)

        # Game modes (only for regular PS5, VIP only has 1v1)
        if self.device.device_type.value == "ps5_regular":
            # 1v1 mode
            self.game_1v1_radio = QRadioButton("1v1 Game Mode")
            self.mode_group.addButton(self.game_1v1_radio, 1)
            layout.addWidget(self.game_1v1_radio)

            # 1v1 price input
            form_1v1 = QFormLayout()
            self.price_1v1_spinbox = QDoubleSpinBox()
            self.price_1v1_spinbox.setRange(0.0, 1000.0)
            self.price_1v1_spinbox.setDecimals(2)
            self.price_1v1_spinbox.setSuffix(" DZD")
            self.price_1v1_spinbox.setValue(100.0)
            form_1v1.addRow("Price per 1v1 game:", self.price_1v1_spinbox)
            layout.addLayout(form_1v1)

            # 2v2 mode
            self.game_2v2_radio = QRadioButton("2v2 Game Mode")
            self.mode_group.addButton(self.game_2v2_radio, 2)
            layout.addWidget(self.game_2v2_radio)

            # 2v2 price input
            form_2v2 = QFormLayout()
            self.price_2v2_spinbox = QDoubleSpinBox()
            self.price_2v2_spinbox.setRange(0.0, 1000.0)
            self.price_2v2_spinbox.setDecimals(2)
            self.price_2v2_spinbox.setSuffix(" DZD")
            self.price_2v2_spinbox.setValue(150.0)
            form_2v2.addRow("Price per 2v2 game:", self.price_2v2_spinbox)
            layout.addLayout(form_2v2)

        else:  # VIP only has 1v1
            # 1v1 mode
            self.game_1v1_radio = QRadioButton("1v1 Game Mode (VIP Only)")
            self.mode_group.addButton(self.game_1v1_radio, 1)
            layout.addWidget(self.game_1v1_radio)

            # 1v1 price input
            form_1v1 = QFormLayout()
            self.price_1v1_spinbox = QDoubleSpinBox()
            self.price_1v1_spinbox.setRange(0.0, 1000.0)
            self.price_1v1_spinbox.setDecimals(2)
            self.price_1v1_spinbox.setSuffix(" DZD")
            self.price_1v1_spinbox.setValue(200.0)  # Higher price for VIP
            form_1v1.addRow("Price per 1v1 game:", self.price_1v1_spinbox)
            layout.addLayout(form_1v1)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def get_session_config(self):
        """Get the selected session configuration."""
        selected_id = self.mode_group.checkedId()

        if selected_id == 0:  # Hourly
            return {
                'mode': 'hourly',
                'price': self.device.price_per_unit
            }
        elif selected_id == 1:  # 1v1
            return {
                'mode': '1v1',
                'price': self.price_1v1_spinbox.value()
            }
        elif selected_id == 2:  # 2v2
            return {
                'mode': '2v2',
                'price': self.price_2v2_spinbox.value()
            }
        else:
            return None

class PS5Panel(QWidget):
    """Panel for managing PS5 gaming stations."""
    
    def __init__(self, data_manager: DataManager):
        super().__init__()
        self.data_manager = data_manager
        self.device_widgets = {}
        self.setup_ui()
        self.load_devices()
        
    def setup_ui(self):
        """Set up the panel UI."""
        # Set solid background for the panel with proper text styling
        self.setStyleSheet("""
            background-color: #f8f9fa;
            color: #2c3e50;
            QLabel {
                color: #2c3e50;
                background-color: transparent;
            }
            QPushButton {
                color: white;
                background-color: #3498db;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # Header
        header_layout = QHBoxLayout()

        # Header with image and title
        header_content = QHBoxLayout()

        # PS5 image
        ps5_image_label = QLabel()
        current_dir = os.path.dirname(os.path.dirname(__file__))
        ps5_image_path = os.path.join(current_dir, "images", "ps5.png")
        if os.path.exists(ps5_image_path):
            pixmap = QPixmap(ps5_image_path)
            scaled_pixmap = pixmap.scaled(65, 65, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            ps5_image_label.setPixmap(scaled_pixmap)
        else:
            ps5_image_label.setText("🎮")
            ps5_image_label.setStyleSheet("font-size: 45px;")

        title_label = QLabel("PS5 Gaming Stations")
        title_font = QFont("Segoe UI", 20)  # Better font and slightly bigger
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: black; margin-left: 10px;")

        header_content.addWidget(ps5_image_label)
        header_content.addWidget(title_label)
        header_content.addStretch()
        
        # Control buttons
        self.create_button = QPushButton("➕ Create Station")
        self.create_button.setMinimumSize(150, 40)
        self.create_button.setProperty("class", "primary")
        self.create_button.clicked.connect(self.create_station)

        self.history_button = QPushButton("📋 History")
        self.history_button.setMinimumSize(120, 40)
        self.history_button.setProperty("class", "info")
        self.history_button.clicked.connect(self.show_history)
        
        header_layout.addLayout(header_content)
        header_layout.addWidget(self.create_button)
        header_layout.addWidget(self.history_button)
        
        layout.addLayout(header_layout)
        
        # Statistics summary
        self.stats_frame = QFrame()
        self.stats_frame.setFrameStyle(QFrame.StyledPanel)
        self.stats_frame.setMaximumHeight(80)
        stats_layout = QHBoxLayout(self.stats_frame)
        
        self.total_stations_label = QLabel("Total Stations: 0")
        self.active_sessions_label = QLabel("Active Sessions: 0")
        self.total_revenue_label = QLabel("Total Revenue: 0.00 DZD")
        
        stats_layout.addWidget(self.total_stations_label)
        stats_layout.addWidget(self.active_sessions_label)
        stats_layout.addWidget(self.total_revenue_label)
        stats_layout.addStretch()
        
        layout.addWidget(self.stats_frame)
        
        # Devices area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.devices_widget = QWidget()
        self.devices_layout = QGridLayout(self.devices_widget)
        self.devices_layout.setSpacing(15)
        self.devices_layout.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        
        self.scroll_area.setWidget(self.devices_widget)
        layout.addWidget(self.scroll_area)
        
        # Empty state message
        self.empty_label = QLabel("No PS5 stations created yet.\nClick 'Create Station' to add your first PS5 gaming station.")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.empty_label.setStyleSheet("color: #7f8c8d; font-size: 14px; font-style: italic;")
        self.empty_label.setVisible(True)
        layout.addWidget(self.empty_label)
        
    def load_devices(self):
        """Load PS5 devices from data manager."""
        ps5_devices = self.data_manager.get_devices_by_type(DeviceType.PS5_REGULAR) + \
                     self.data_manager.get_devices_by_type(DeviceType.PS5_VIP)
                     
        for device in ps5_devices:
            self.add_device_widget(device)
            
        self.update_display()
        
    def create_station(self):
        """Create a new PS5 station."""
        dialog = CreatePS5Dialog(self)
        if dialog.exec_() == QDialog.Accepted:
            values = dialog.get_values()
            
            if not values['name']:
                QMessageBox.warning(self, "Error", "Please enter a station name.")
                return
                
            # Check for duplicate names
            existing_names = [device.name for device in self.data_manager.get_all_devices()]
            if values['name'] in existing_names:
                QMessageBox.warning(self, "Error", "A device with this name already exists.")
                return
                
            # Create device
            device_type = DeviceType.PS5_VIP if values['is_vip'] else DeviceType.PS5_REGULAR
            device = Device(values['name'], device_type, values['price_per_hour'])
            
            # Add to data manager
            self.data_manager.add_device(device)
            
            # Add to UI
            self.add_device_widget(device)
            self.update_display()
            
    def add_device_widget(self, device: Device):
        """Add a device widget to the panel."""
        device_widget = PS5DeviceWidget(device)
        device_widget.device_deleted.connect(self.remove_device_widget)
        device_widget.device_updated.connect(self.on_device_updated)
        
        self.device_widgets[device.id] = device_widget
        
        # Add to grid layout
        row = len(self.device_widgets) // 3
        col = (len(self.device_widgets) - 1) % 3
        self.devices_layout.addWidget(device_widget, row, col)
        
    def remove_device_widget(self, device_id: str):
        """Remove a device widget from the panel."""
        if device_id in self.device_widgets:
            widget = self.device_widgets[device_id]
            self.devices_layout.removeWidget(widget)
            widget.deleteLater()
            del self.device_widgets[device_id]
            
            # Remove from data manager
            self.data_manager.remove_device(device_id)
            
            # Reorganize grid
            self.reorganize_grid()
            self.update_display()
            
    def reorganize_grid(self):
        """Reorganize the grid layout after removing a widget."""
        # Clear layout
        for i in reversed(range(self.devices_layout.count())):
            self.devices_layout.itemAt(i).widget().setParent(None)
            
        # Re-add widgets
        for i, widget in enumerate(self.device_widgets.values()):
            row = i // 3
            col = i % 3
            self.devices_layout.addWidget(widget, row, col)
            
    def update_display(self):
        """Update the display with current statistics."""
        ps5_devices = self.data_manager.get_devices_by_type(DeviceType.PS5_REGULAR) + \
                     self.data_manager.get_devices_by_type(DeviceType.PS5_VIP)
                     
        total_stations = len(ps5_devices)
        active_sessions = sum(1 for device in ps5_devices if device.current_session)
        total_revenue = sum(device.get_total_revenue() for device in ps5_devices)
        
        self.total_stations_label.setText(f"Total Stations: {total_stations}")
        self.active_sessions_label.setText(f"Active Sessions: {active_sessions}")
        self.total_revenue_label.setText(f"Total Revenue: {total_revenue:.2f} DZD")
        
        # Show/hide empty state
        self.empty_label.setVisible(total_stations == 0)
        self.scroll_area.setVisible(total_stations > 0)
        
    def show_history(self):
        """Show history for PS5 devices."""
        ps5_devices = self.data_manager.get_devices_by_type(DeviceType.PS5_REGULAR) + \
                     self.data_manager.get_devices_by_type(DeviceType.PS5_VIP)
                     
        if not ps5_devices:
            QMessageBox.information(self, "No History", "No PS5 stations found.")
            return
            
        history_panel = HistoryPanel(ps5_devices, "PS5 Stations History", self)
        history_panel.exec_()

    def on_device_updated(self):
        """Handle device updates by saving data and refreshing display."""
        self.data_manager.save_data()
        self.update_display()
