"""
AI Coordinator - Main interface for all AI modules
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

from .smart_pricing import SmartPricingEngine
from .promotion_engine import PromotionAnalyzer
from .anomaly_detector import BusinessAnomalyDetector


class AICoordinator:
    """Main coordinator for all AI-powered business intelligence features."""
    
    def __init__(self):
        self.pricing_engine = SmartPricingEngine()
        self.promotion_analyzer = PromotionAnalyzer()
        self.anomaly_detector = BusinessAnomalyDetector()
        
        # Initialize anomaly detector with synthetic data
        self.anomaly_detector.fit_models()
        
    def get_comprehensive_analysis(self, business_data: Dict, 
                                 products_data: Dict = None,
                                 current_prices: Dict = None) -> Dict:
        """
        Get comprehensive AI analysis including pricing, promotions, and anomalies.
        
        Args:
            business_data: Current business metrics
            products_data: Product sales data
            current_prices: Current pricing structure
            
        Returns:
            Complete AI analysis and recommendations
        """
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'pricing_analysis': {},
            'promotion_recommendations': {},
            'anomaly_detection': {},
            'executive_summary': {},
            'action_items': []
        }
        
        try:
            # 1. Smart Pricing Analysis
            pricing_analysis = self.pricing_engine.get_pricing_recommendations(
                business_data, current_prices
            )
            analysis['pricing_analysis'] = pricing_analysis
            
            # 2. Promotion and Rule-based Recommendations
            promotion_recommendations = self.promotion_analyzer.analyze_and_recommend(
                business_data, products_data
            )
            analysis['promotion_recommendations'] = promotion_recommendations
            
            # 3. Anomaly Detection
            anomaly_results = self.anomaly_detector.detect_anomalies(
                business_data, products_data
            )
            analysis['anomaly_detection'] = anomaly_results
            
            # 4. Generate Executive Summary
            analysis['executive_summary'] = self._generate_executive_summary(
                pricing_analysis, promotion_recommendations, anomaly_results, business_data
            )
            
            # 5. Generate Action Items
            analysis['action_items'] = self._generate_action_items(
                pricing_analysis, promotion_recommendations, anomaly_results
            )
            
        except Exception as e:
            analysis['error'] = f"AI Analysis Error: {str(e)}"
            analysis['executive_summary'] = {
                'status': 'error',
                'message': 'AI analysis temporarily unavailable. Using fallback recommendations.'
            }
        
        return analysis
    
    def _generate_executive_summary(self, pricing_analysis: Dict, 
                                  promotion_recommendations: Dict,
                                  anomaly_results: Dict,
                                  business_data: Dict) -> Dict:
        """Generate executive summary of AI analysis."""
        
        summary = {
            'business_health': 'good',
            'key_insights': [],
            'priority_actions': [],
            'revenue_outlook': 'stable',
            'risk_level': 'low'
        }
        
        # Analyze business health
        total_revenue = business_data.get('total_revenue', 0)
        total_sessions = business_data.get('total_sessions', 0)

        # More realistic thresholds
        if total_revenue > 2000 and total_sessions > 30:
            summary['business_health'] = 'excellent'
            summary['revenue_outlook'] = 'growing'
        elif total_revenue > 1000 and total_sessions > 15:
            summary['business_health'] = 'good'
            summary['revenue_outlook'] = 'stable'
        elif total_revenue > 500 and total_sessions > 8:
            summary['business_health'] = 'fair'
            summary['revenue_outlook'] = 'cautious'
        else:
            summary['business_health'] = 'needs_attention'
            summary['revenue_outlook'] = 'declining'
            summary['risk_level'] = 'medium'
        
        # Key insights from pricing
        if pricing_analysis.get('price_changes'):
            significant_changes = [
                change for change in pricing_analysis['price_changes'].values()
                if abs(change.get('change_percent', 0)) > 10
            ]
            if significant_changes:
                summary['key_insights'].append(
                    f"💰 Pricing optimization suggests {len(significant_changes)} significant price adjustments"
                )
        
        # Key insights from promotions
        promotions = promotion_recommendations.get('promotions', [])
        if promotions:
            summary['key_insights'].append(
                f"🎯 {len(promotions)} promotional opportunities identified"
            )
        
        vip_customers = promotion_recommendations.get('vip_customers', [])
        if vip_customers:
            summary['key_insights'].append(
                f"👑 {len(vip_customers)} VIP customers detected - high value retention opportunity"
            )
        
        # Key insights from anomalies
        if anomaly_results.get('is_anomaly'):
            anomalies = anomaly_results.get('anomalies_detected', [])
            summary['key_insights'].append(
                f"⚠️ {len(anomalies)} unusual patterns detected requiring attention"
            )
            summary['risk_level'] = 'high' if len(anomalies) > 2 else 'medium'
        
        # Priority actions
        if summary['business_health'] in ['needs_attention', 'fair']:
            summary['priority_actions'].append("Implement immediate revenue boosting strategies")
        
        if anomaly_results.get('is_anomaly'):
            summary['priority_actions'].append("Investigate unusual business patterns")
        
        if len(promotions) > 3:
            summary['priority_actions'].append("Execute high-impact promotional campaigns")
        
        return summary
    
    def _generate_action_items(self, pricing_analysis: Dict,
                             promotion_recommendations: Dict,
                             anomaly_results: Dict) -> List[Dict]:
        """Generate prioritized action items."""
        
        action_items = []
        
        # High priority: Anomalies
        if anomaly_results.get('is_anomaly'):
            for anomaly in anomaly_results.get('anomalies_detected', []):
                action_items.append({
                    'priority': 'high',
                    'category': 'anomaly',
                    'title': f"Address {anomaly['type'].replace('_', ' ').title()}",
                    'description': anomaly['description'],
                    'timeline': 'immediate'
                })
        
        # Medium priority: Pricing changes
        price_changes = pricing_analysis.get('price_changes', {})
        for device, change in price_changes.items():
            if abs(change.get('change_percent', 0)) > 15:
                action_items.append({
                    'priority': 'medium',
                    'category': 'pricing',
                    'title': f"Adjust {device.replace('_', ' ').title()} Pricing",
                    'description': f"Change from {change['current']:.2f} to {change['optimized']:.2f} DZD ({change['change_percent']:+.1f}%)",
                    'timeline': 'this_week'
                })
        
        # Medium priority: High-impact promotions
        promotions = promotion_recommendations.get('promotions', [])
        high_impact_promotions = [p for p in promotions if p.get('type') in ['revenue_boost', 'low_utilization']]
        
        for promo in high_impact_promotions[:3]:  # Top 3 promotions
            action_items.append({
                'priority': 'medium',
                'category': 'promotion',
                'title': f"Launch {promo['type'].replace('_', ' ').title()} Promotion",
                'description': promo['promotion'],
                'timeline': 'this_week'
            })
        
        # Low priority: VIP customer engagement
        vip_customers = promotion_recommendations.get('vip_customers', [])
        if vip_customers:
            action_items.append({
                'priority': 'low',
                'category': 'customer_retention',
                'title': f"Engage {len(vip_customers)} VIP Customers",
                'description': "Implement VIP customer retention and upselling programs",
                'timeline': 'this_month'
            })
        
        # Sort by priority
        priority_order = {'high': 1, 'medium': 2, 'low': 3}
        action_items.sort(key=lambda x: priority_order.get(x['priority'], 4))
        
        return action_items
    
    def get_pricing_recommendations_only(self, business_data: Dict, 
                                       current_prices: Dict = None) -> Dict:
        """Get only pricing recommendations (faster for real-time updates)."""
        return self.pricing_engine.get_pricing_recommendations(business_data, current_prices)
    
    def get_promotions_only(self, business_data: Dict, products_data: Dict = None) -> Dict:
        """Get only promotion recommendations."""
        return self.promotion_analyzer.analyze_and_recommend(business_data, products_data)
    
    def check_anomalies_only(self, business_data: Dict, products_data: Dict = None) -> Dict:
        """Get only anomaly detection results."""
        return self.anomaly_detector.detect_anomalies(business_data, products_data)
    
    def export_analysis_report(self, analysis: Dict, format: str = 'json') -> str:
        """
        Export analysis report in specified format.
        
        Args:
            analysis: Complete analysis results
            format: Export format ('json', 'summary')
            
        Returns:
            Formatted report string
        """
        if format == 'json':
            return json.dumps(analysis, indent=2, default=str)
        
        elif format == 'summary':
            summary = analysis.get('executive_summary', {})
            report = f"""
🤖 AI BUSINESS INTELLIGENCE REPORT
Generated: {analysis.get('timestamp', 'Unknown')}

📊 BUSINESS HEALTH: {summary.get('business_health', 'Unknown').upper()}
📈 REVENUE OUTLOOK: {summary.get('revenue_outlook', 'Unknown').upper()}
⚠️ RISK LEVEL: {summary.get('risk_level', 'Unknown').upper()}

🔍 KEY INSIGHTS:
{chr(10).join('• ' + insight for insight in summary.get('key_insights', []))}

🎯 PRIORITY ACTIONS:
{chr(10).join('• ' + action for action in summary.get('priority_actions', []))}

📋 ACTION ITEMS:
"""
            
            action_items = analysis.get('action_items', [])
            for item in action_items[:5]:  # Top 5 action items
                priority_emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(item['priority'], '⚪')
                report += f"{priority_emoji} {item['title']}: {item['description']}\n"
            
            return report
        
        else:
            return "Unsupported format. Use 'json' or 'summary'."
