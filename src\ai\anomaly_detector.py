"""
Anomaly Detection Module using PyOD
"""

import numpy as np
import pandas as pd
from pyod.models.iforest import IForest
from pyod.models.lof import LOF
from pyod.models.ocsvm import OCSVM
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')


class BusinessAnomalyDetector:
    """Detect unusual patterns in business operations."""
    
    def __init__(self):
        self.models = {
            'isolation_forest': IForest(contamination=0.1, random_state=42),
            'local_outlier_factor': LOF(contamination=0.1),
            'one_class_svm': OCSVM(contamination=0.1)
        }
        self.is_fitted = False
        self.feature_names = []
        
    def prepare_features(self, business_data: Dict, products_data: Dict = None) -> np.ndarray:
        """
        Prepare feature matrix for anomaly detection.
        
        Args:
            business_data: Business metrics data
            products_data: Product sales data
            
        Returns:
            Feature matrix for anomaly detection
        """
        features = []
        feature_names = []
        
        # Revenue features
        total_revenue = business_data.get('total_revenue', 0)
        features.append(total_revenue)
        feature_names.append('total_revenue')
        
        # Session features
        total_sessions = business_data.get('total_sessions', 0)
        features.append(total_sessions)
        feature_names.append('total_sessions')
        
        # Average session value
        avg_session_value = total_revenue / max(total_sessions, 1)
        features.append(avg_session_value)
        feature_names.append('avg_session_value')
        
        # Device utilization features
        sessions_by_type = business_data.get('sessions_by_type', {})
        device_types = ['ps5_1v1', 'ps5_2v2', 'arcade', 'pool', 'babyfoot', 'other']
        
        for device in device_types:
            device_sessions = sessions_by_type.get(device, 0)
            features.append(device_sessions)
            feature_names.append(f'{device}_sessions')
            
        # Hourly distribution features
        hourly_usage = business_data.get('hourly_usage', {})
        
        # Peak hour intensity
        max_hourly = max(hourly_usage.values()) if hourly_usage else 0
        features.append(max_hourly)
        feature_names.append('peak_hour_intensity')
        
        # Usage spread (standard deviation of hourly usage)
        hourly_values = list(hourly_usage.values()) if hourly_usage else [0]
        usage_spread = np.std(hourly_values)
        features.append(usage_spread)
        feature_names.append('usage_spread')
        
        # Time-based features
        now = datetime.now()
        features.append(now.hour)
        feature_names.append('current_hour')
        features.append(now.weekday())
        feature_names.append('day_of_week')
        
        # Product features (if available)
        if products_data:
            product_revenue = products_data.get('total_revenue', 0)
            product_sales = products_data.get('total_sales', 0)
            
            features.append(product_revenue)
            feature_names.append('product_revenue')
            features.append(product_sales)
            feature_names.append('product_sales')
            
            # Product-to-session ratio
            product_session_ratio = product_sales / max(total_sessions, 1)
            features.append(product_session_ratio)
            feature_names.append('product_session_ratio')
        else:
            # Add zeros for missing product features
            features.extend([0, 0, 0])
            feature_names.extend(['product_revenue', 'product_sales', 'product_session_ratio'])
        
        self.feature_names = feature_names
        return np.array(features).reshape(1, -1)
    
    def generate_synthetic_training_data(self, n_samples: int = 100) -> np.ndarray:
        """
        Generate synthetic training data for anomaly detection.
        
        Args:
            n_samples: Number of synthetic samples to generate
            
        Returns:
            Synthetic training data matrix
        """
        np.random.seed(42)
        
        # Generate realistic business data patterns
        training_data = []
        
        for _ in range(n_samples):
            # Revenue: Normal distribution around 500-2000 DZD
            revenue = np.random.normal(1000, 300)
            revenue = max(0, revenue)
            
            # Sessions: Poisson distribution
            sessions = np.random.poisson(15)
            sessions = max(1, sessions)
            
            # Average session value
            avg_value = revenue / sessions
            
            # Device sessions (multinomial distribution)
            device_probs = [0.3, 0.2, 0.2, 0.15, 0.1, 0.05]  # PS5, PS5_2v2, arcade, pool, babyfoot, other
            device_sessions = np.random.multinomial(sessions, device_probs)
            
            # Peak hour intensity
            peak_intensity = np.random.gamma(2, 2)
            
            # Usage spread
            usage_spread = np.random.exponential(2)
            
            # Time features
            hour = np.random.randint(0, 24)
            day_of_week = np.random.randint(0, 7)
            
            # Product features
            product_revenue = np.random.normal(revenue * 0.2, revenue * 0.1)
            product_revenue = max(0, product_revenue)
            product_sales = np.random.poisson(sessions * 0.8)
            product_session_ratio = product_sales / sessions
            
            # Combine features
            sample = [revenue, sessions, avg_value] + list(device_sessions) + \
                    [peak_intensity, usage_spread, hour, day_of_week, 
                     product_revenue, product_sales, product_session_ratio]
            
            training_data.append(sample)
        
        return np.array(training_data)
    
    def fit_models(self, training_data: np.ndarray = None):
        """
        Fit anomaly detection models.
        
        Args:
            training_data: Training data matrix (optional, will generate synthetic if None)
        """
        if training_data is None:
            training_data = self.generate_synthetic_training_data()
        
        # Fit all models
        for name, model in self.models.items():
            try:
                model.fit(training_data)
            except Exception as e:
                print(f"Warning: Failed to fit {name}: {e}")
        
        self.is_fitted = True
    
    def detect_anomalies(self, business_data: Dict, products_data: Dict = None) -> Dict:
        """
        Detect anomalies in current business data.
        
        Args:
            business_data: Current business metrics
            products_data: Current product data
            
        Returns:
            Dictionary with anomaly detection results
        """
        if not self.is_fitted:
            self.fit_models()
        
        # Prepare current data
        current_features = self.prepare_features(business_data, products_data)
        
        # Run anomaly detection with each model
        results = {
            'is_anomaly': False,
            'anomaly_scores': {},
            'consensus_score': 0.0,
            'anomalies_detected': [],
            'recommendations': []
        }
        
        anomaly_votes = 0
        total_score = 0.0
        
        for name, model in self.models.items():
            try:
                # Get anomaly score and prediction
                anomaly_score = model.decision_function(current_features)[0]
                is_outlier = model.predict(current_features)[0] == 1
                
                results['anomaly_scores'][name] = float(anomaly_score)
                
                if is_outlier:
                    anomaly_votes += 1
                
                total_score += abs(anomaly_score)
                
            except Exception as e:
                print(f"Warning: Anomaly detection failed for {name}: {e}")
                results['anomaly_scores'][name] = 0.0
        
        # Consensus decision
        results['is_anomaly'] = anomaly_votes >= 2  # Majority vote
        results['consensus_score'] = total_score / len(self.models)
        
        # Analyze specific anomalies
        if results['is_anomaly']:
            anomalies = self._analyze_specific_anomalies(business_data, products_data)
            results['anomalies_detected'] = anomalies
            results['recommendations'] = self._generate_anomaly_recommendations(anomalies)
        
        return results
    
    def _analyze_specific_anomalies(self, business_data: Dict, products_data: Dict = None) -> List[Dict]:
        """Analyze what specific metrics are anomalous."""
        anomalies = []
        
        # Revenue anomalies
        revenue = business_data.get('total_revenue', 0)
        if revenue > 3000:
            anomalies.append({
                'type': 'high_revenue',
                'value': revenue,
                'description': f'Unusually high revenue: {revenue:.2f} DZD'
            })
        elif revenue < 100:
            anomalies.append({
                'type': 'low_revenue',
                'value': revenue,
                'description': f'Unusually low revenue: {revenue:.2f} DZD'
            })
        
        # Session anomalies
        sessions = business_data.get('total_sessions', 0)
        if sessions > 50:
            anomalies.append({
                'type': 'high_sessions',
                'value': sessions,
                'description': f'Unusually high session count: {sessions}'
            })
        elif sessions < 2:
            anomalies.append({
                'type': 'low_sessions',
                'value': sessions,
                'description': f'Unusually low session count: {sessions}'
            })
        
        # Device usage anomalies
        sessions_by_type = business_data.get('sessions_by_type', {})
        total_sessions = sum(sessions_by_type.values()) or 1
        
        for device, device_sessions in sessions_by_type.items():
            usage_pct = (device_sessions / total_sessions) * 100
            if usage_pct > 70:
                anomalies.append({
                    'type': 'device_dominance',
                    'device': device,
                    'value': usage_pct,
                    'description': f'{device.replace("_", " ").title()} dominates usage: {usage_pct:.1f}%'
                })
        
        # Product anomalies
        if products_data:
            product_revenue = products_data.get('total_revenue', 0)
            if revenue > 0:
                product_ratio = product_revenue / revenue
                if product_ratio > 0.5:
                    anomalies.append({
                        'type': 'high_product_sales',
                        'value': product_ratio,
                        'description': f'Unusually high product sales ratio: {product_ratio:.1%}'
                    })
        
        return anomalies
    
    def _generate_anomaly_recommendations(self, anomalies: List[Dict]) -> List[str]:
        """Generate recommendations based on detected anomalies."""
        recommendations = []
        
        for anomaly in anomalies:
            anomaly_type = anomaly['type']
            
            if anomaly_type == 'high_revenue':
                recommendations.append("🎉 Exceptional revenue day! Analyze what drove success and replicate strategies.")
                
            elif anomaly_type == 'low_revenue':
                recommendations.append("⚠️ Low revenue alert! Consider emergency promotions or check for operational issues.")
                
            elif anomaly_type == 'high_sessions':
                recommendations.append("📈 High demand detected! Ensure adequate staffing and consider capacity expansion.")
                
            elif anomaly_type == 'low_sessions':
                recommendations.append("📉 Low activity alert! Implement immediate marketing campaigns or check for external factors.")
                
            elif anomaly_type == 'device_dominance':
                device = anomaly['device']
                recommendations.append(f"🎮 {device.replace('_', ' ').title()} is dominating usage. Consider promoting other devices or adjusting capacity.")
                
            elif anomaly_type == 'high_product_sales':
                recommendations.append("🛒 Exceptional product sales! Ensure adequate inventory and consider expanding product offerings.")
        
        return recommendations
