"""
Device models for the GameShop Manager application.
"""

from datetime import datetime, timedelta
from enum import Enum
from typing import Optional, List, Dict, Any
import json

class DeviceType(Enum):
    """Enumeration of device types."""
    PS5_REGULAR = "ps5_regular"
    PS5_VIP = "ps5_vip"
    ARCADE = "arcade"
    BABYFOOT = "babyfoot"
    POOL = "pool"
    OTHER = "other"

class SessionState(Enum):
    """Enumeration of session states."""
    IDLE = "idle"
    ACTIVE = "active"
    PAUSED = "paused"
    ENDED = "ended"

class PricingMode(Enum):
    """Enumeration of pricing modes."""
    HOURLY = "hourly"  # For PS5, Arcade, Pool, Other
    PER_GAME = "per_game"  # For Babyfoot, PS5 games
    PS5_1V1 = "ps5_1v1"  # PS5 1v1 game mode
    PS5_2V2 = "ps5_2v2"  # PS5 2v2 game mode

class Session:
    """Represents a gaming session."""

    def __init__(self, device_id: str, pricing_mode: PricingMode, price_per_unit: float, game_mode: str = None, booked_minutes: int = None):
        self.id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{device_id}"
        self.device_id = device_id
        self.pricing_mode = pricing_mode
        self.price_per_unit = price_per_unit  # DZD per hour or per game
        self.game_mode = game_mode  # For PS5: "1v1", "2v2", or None for hourly
        self.booked_minutes = booked_minutes  # For time booking notifications
        self.start_time = datetime.now()
        self.end_time: Optional[datetime] = None
        self.pause_time: Optional[datetime] = None
        self.total_paused_duration = timedelta()
        self.state = SessionState.ACTIVE
        self.games_played = 0  # For babyfoot and PS5 games
        self.total_cost = 0.0

        
    def pause(self):
        """Pause the session."""
        if self.state == SessionState.ACTIVE:
            self.pause_time = datetime.now()
            self.state = SessionState.PAUSED
            
    def resume(self):
        """Resume the session."""
        if self.state == SessionState.PAUSED and self.pause_time:
            self.total_paused_duration += datetime.now() - self.pause_time
            self.pause_time = None
            self.state = SessionState.ACTIVE
            
    def end(self):
        """End the session and calculate total cost."""
        if self.state in [SessionState.ACTIVE, SessionState.PAUSED]:
            self.end_time = datetime.now()
            if self.pause_time:  # If ending while paused
                self.total_paused_duration += self.end_time - self.pause_time
                self.pause_time = None
            self.state = SessionState.ENDED
            self._calculate_cost()
            
    def add_game(self):
        """Add a game (for babyfoot and PS5 games)."""
        if self.pricing_mode in [PricingMode.PER_GAME, PricingMode.PS5_1V1, PricingMode.PS5_2V2]:
            self.games_played += 1
            self.total_cost = self.games_played * self.price_per_unit

            
    def get_duration(self) -> timedelta:
        """Get the active duration of the session."""
        if self.state == SessionState.IDLE:
            return timedelta()
        
        end_time = self.end_time or datetime.now()
        if self.state == SessionState.PAUSED and self.pause_time:
            # Include current pause time
            total_paused = self.total_paused_duration + (datetime.now() - self.pause_time)
        else:
            total_paused = self.total_paused_duration
            
        return end_time - self.start_time - total_paused
        
    def _calculate_cost(self):
        """Calculate the total cost based on pricing mode."""
        if self.pricing_mode == PricingMode.HOURLY:
            duration_hours = self.get_duration().total_seconds() / 3600
            self.total_cost = duration_hours * self.price_per_unit
        elif self.pricing_mode in [PricingMode.PER_GAME, PricingMode.PS5_1V1, PricingMode.PS5_2V2]:
            # For game modes, cost is fixed: games_played * price_per_unit
            self.total_cost = self.games_played * self.price_per_unit

            
    def get_current_cost(self) -> float:
        """Get the current cost (for active sessions)."""
        if self.state == SessionState.ENDED:
            return self.total_cost
        elif self.pricing_mode == PricingMode.HOURLY:
            duration_hours = self.get_duration().total_seconds() / 3600
            return duration_hours * self.price_per_unit
        else:  # PER_GAME, PS5_1V1, PS5_2V2
            return self.games_played * self.price_per_unit

    def is_time_up(self) -> bool:
        """Check if booked time has elapsed."""
        if not self.booked_minutes or self.state != SessionState.ACTIVE:
            return False

        elapsed_minutes = self.get_duration().total_seconds() / 60
        return elapsed_minutes >= self.booked_minutes

    def get_remaining_time(self) -> timedelta:
        """Get remaining booked time."""
        if not self.booked_minutes:
            return timedelta()

        elapsed_minutes = self.get_duration().total_seconds() / 60
        remaining_minutes = max(0, self.booked_minutes - elapsed_minutes)
        return timedelta(minutes=remaining_minutes)
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary for serialization."""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'pricing_mode': self.pricing_mode.value,
            'price_per_unit': self.price_per_unit,
            'game_mode': self.game_mode,
            'booked_minutes': self.booked_minutes,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'pause_time': self.pause_time.isoformat() if self.pause_time else None,
            'total_paused_duration': self.total_paused_duration.total_seconds(),
            'state': self.state.value,
            'games_played': self.games_played,
            'total_cost': self.total_cost
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Session':
        """Create session from dictionary."""
        session = cls.__new__(cls)
        session.id = data['id']
        session.device_id = data['device_id']
        session.pricing_mode = PricingMode(data['pricing_mode'])
        session.price_per_unit = data['price_per_unit']
        session.game_mode = data.get('game_mode')  # Handle backward compatibility
        session.booked_minutes = data.get('booked_minutes')  # Handle backward compatibility
        session.start_time = datetime.fromisoformat(data['start_time'])
        session.end_time = datetime.fromisoformat(data['end_time']) if data['end_time'] else None
        session.pause_time = datetime.fromisoformat(data['pause_time']) if data['pause_time'] else None
        session.total_paused_duration = timedelta(seconds=data['total_paused_duration'])
        session.state = SessionState(data['state'])
        session.games_played = data['games_played']
        session.total_cost = data['total_cost']
        return session

class Device:
    """Represents a gaming device/station."""
    
    def __init__(self, name: str, device_type: DeviceType, price_per_unit: float, custom_image: str = None):
        self.id = f"device_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{name.replace(' ', '_')}"
        self.name = name
        self.device_type = device_type
        self.price_per_unit = price_per_unit  # DZD per hour or per game
        self.custom_image = custom_image  # Custom image filename for the device
        self.current_session: Optional[Session] = None
        self.session_history: List[Session] = []
        self.created_at = datetime.now()
        self.is_active = True

        # PS5 game mode pricing
        if device_type.value in ["ps5_regular", "ps5_vip"]:
            self.game_1v1_price = 200.0 if device_type.value == "ps5_vip" else 100.0
            if device_type.value == "ps5_regular":
                self.game_2v2_price = 150.0
        
    @property
    def pricing_mode(self) -> PricingMode:
        """Get the default pricing mode based on device type."""
        if self.device_type == DeviceType.BABYFOOT:
            return PricingMode.PER_GAME
        else:
            return PricingMode.HOURLY
            
    def start_session(self, pricing_mode: PricingMode = None, price_per_unit: float = None, game_mode: str = None, booked_minutes: int = None) -> Session:
        """Start a new session."""
        if self.current_session and self.current_session.state in [SessionState.ACTIVE, SessionState.PAUSED]:
            raise ValueError("Cannot start new session: current session is still active")

        # Use provided parameters or defaults
        session_pricing_mode = pricing_mode or self.pricing_mode
        session_price = price_per_unit or self.price_per_unit

        self.current_session = Session(self.id, session_pricing_mode, session_price, game_mode, booked_minutes)
        return self.current_session

    def start_ps5_game_session(self, game_mode: str, price_per_game: float) -> Session:
        """Start a PS5 game session (1v1 or 2v2)."""
        if game_mode == "1v1":
            pricing_mode = PricingMode.PS5_1V1
        elif game_mode == "2v2":
            pricing_mode = PricingMode.PS5_2V2
        else:
            raise ValueError("Invalid game mode. Must be '1v1' or '2v2'")

        return self.start_session(pricing_mode, price_per_game, game_mode)
        
    def end_session(self):
        """End the current session."""
        if self.current_session:
            self.current_session.end()
            self.session_history.append(self.current_session)
            self.current_session = None
            
    def pause_session(self):
        """Pause the current session."""
        if self.current_session:
            self.current_session.pause()
            
    def resume_session(self):
        """Resume the current session."""
        if self.current_session:
            self.current_session.resume()
            
    def add_game(self):
        """Add a game to current session (for babyfoot and PS5 games)."""
        if self.current_session and self.current_session.pricing_mode in [PricingMode.PER_GAME, PricingMode.PS5_1V1, PricingMode.PS5_2V2]:
            self.current_session.add_game()
            
    def get_current_cost(self) -> float:
        """Get current session cost."""
        if self.current_session:
            return self.current_session.get_current_cost()
        return 0.0
        
    def get_total_revenue(self) -> float:
        """Get total revenue from this device."""
        total = sum(session.total_cost for session in self.session_history)
        if self.current_session:
            total += self.current_session.get_current_cost()
        return total
        
    def get_total_sessions(self) -> int:
        """Get total number of sessions."""
        count = len(self.session_history)
        if self.current_session:
            count += 1
        return count
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert device to dictionary for serialization."""
        data = {
            'id': self.id,
            'name': self.name,
            'device_type': self.device_type.value,
            'price_per_unit': self.price_per_unit,
            'custom_image': self.custom_image,
            'current_session': self.current_session.to_dict() if self.current_session else None,
            'session_history': [session.to_dict() for session in self.session_history],
            'created_at': self.created_at.isoformat(),
            'is_active': self.is_active
        }

        # Add PS5 game pricing if applicable
        if hasattr(self, 'game_1v1_price'):
            data['game_1v1_price'] = self.game_1v1_price
        if hasattr(self, 'game_2v2_price'):
            data['game_2v2_price'] = self.game_2v2_price

        return data
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Device':
        """Create device from dictionary."""
        device = cls.__new__(cls)
        device.id = data['id']
        device.name = data['name']
        device.device_type = DeviceType(data['device_type'])
        device.price_per_unit = data['price_per_unit']
        device.custom_image = data.get('custom_image')  # Use get() for backward compatibility
        device.current_session = Session.from_dict(data['current_session']) if data['current_session'] else None
        device.session_history = [Session.from_dict(s) for s in data['session_history']]
        device.created_at = datetime.fromisoformat(data['created_at'])
        device.is_active = data['is_active']

        # Load PS5 game pricing if available
        if 'game_1v1_price' in data:
            device.game_1v1_price = data['game_1v1_price']
        elif device.device_type.value in ["ps5_regular", "ps5_vip"]:
            # Set default if not in data (backward compatibility)
            device.game_1v1_price = 200.0 if device.device_type.value == "ps5_vip" else 100.0

        if 'game_2v2_price' in data:
            device.game_2v2_price = data['game_2v2_price']
        elif device.device_type.value == "ps5_regular":
            # Set default if not in data (backward compatibility)
            device.game_2v2_price = 150.0

        return device
