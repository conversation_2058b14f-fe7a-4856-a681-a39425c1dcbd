#!/usr/bin/env python3
"""
Ωmega GameZone - Main Application Entry Point
A comprehensive management system for gaming stations including PS5, Arcade, Babyfoot, and other devices.
"""

import sys
import os
from PySide2.QtWidgets import QApplication
from PySide2.QtCore import Qt
from PySide2.QtGui import QIcon

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.main_window import MainWindow
from src.utils.config_manager import Config<PERSON>anager

def main():
    """Main application entry point."""
    # Load configuration
    config_manager = ConfigManager()
    shop_name = config_manager.get_shop_name()
    version = config_manager.get("app_settings", "version", "1.0.0")
    
    app = QApplication(sys.argv)
    app.setApplicationName(shop_name)
    app.setApplicationVersion(version)
    app.setOrganizationName(shop_name)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    # Start the application event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
