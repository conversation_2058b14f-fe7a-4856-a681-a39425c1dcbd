"""
Device widget for displaying and controlling individual devices.
"""

from PySide2.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QLabel, QFrame, QSpinBox, QDoubleSpinBox,
                               QMessageBox, QDialog, QFormLayout, QLineEdit,
                               QDialogButtonBox, QGridLayout, QInputDialog)
from PySide2.QtCore import Qt, QTimer, Signal
from PySide2.QtGui import QFont, QPalette, QPixmap
from datetime import datetime, timedelta
import os

from ..models.device import Device, SessionState, PricingMode

class EditDeviceDialog(QDialog):
    """Dialog for editing device properties."""
    
    def __init__(self, device: Device, parent=None):
        super().__init__(parent)
        self.device = device
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Edit Device")
        self.setModal(True)
        self.resize(350, 300)

        layout = QVBoxLayout(self)

        # Form layout
        form_layout = QFormLayout()

        # Device name
        self.name_edit = QLineEdit(self.device.name)
        form_layout.addRow("Name:", self.name_edit)

        # For PS5 devices, show multiple pricing options
        if self.device.device_type.value in ["ps5_regular", "ps5_vip"]:
            # Hourly price
            self.hourly_price_spinbox = QDoubleSpinBox()
            self.hourly_price_spinbox.setRange(0.0, 10000.0)
            self.hourly_price_spinbox.setDecimals(2)
            self.hourly_price_spinbox.setSuffix(" DZD")
            self.hourly_price_spinbox.setValue(self.device.price_per_unit)
            form_layout.addRow("Price per Hour:", self.hourly_price_spinbox)

            # 1v1 game price
            self.game_1v1_price_spinbox = QDoubleSpinBox()
            self.game_1v1_price_spinbox.setRange(0.0, 1000.0)
            self.game_1v1_price_spinbox.setDecimals(2)
            self.game_1v1_price_spinbox.setSuffix(" DZD")
            # Set default based on device type
            default_1v1 = 200.0 if self.device.device_type.value == "ps5_vip" else 100.0
            self.game_1v1_price_spinbox.setValue(getattr(self.device, 'game_1v1_price', default_1v1))
            form_layout.addRow("1v1 Game Price:", self.game_1v1_price_spinbox)

            # 2v2 game price (only for regular PS5)
            if self.device.device_type.value == "ps5_regular":
                self.game_2v2_price_spinbox = QDoubleSpinBox()
                self.game_2v2_price_spinbox.setRange(0.0, 1000.0)
                self.game_2v2_price_spinbox.setDecimals(2)
                self.game_2v2_price_spinbox.setSuffix(" DZD")
                self.game_2v2_price_spinbox.setValue(getattr(self.device, 'game_2v2_price', 150.0))
                form_layout.addRow("2v2 Game Price:", self.game_2v2_price_spinbox)
        else:
            # Regular price per unit for other devices
            self.price_spinbox = QDoubleSpinBox()
            self.price_spinbox.setRange(0.0, 10000.0)
            self.price_spinbox.setDecimals(2)
            self.price_spinbox.setSuffix(" DZD")
            self.price_spinbox.setValue(self.device.price_per_unit)

            if self.device.pricing_mode == PricingMode.HOURLY:
                form_layout.addRow("Price per Hour:", self.price_spinbox)
            else:
                form_layout.addRow("Price per Game:", self.price_spinbox)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
    def get_values(self):
        """Get the edited values."""
        values = {
            'name': self.name_edit.text().strip(),
        }

        # Handle PS5 devices with multiple pricing
        if self.device.device_type.value in ["ps5_regular", "ps5_vip"]:
            values['price_per_unit'] = self.hourly_price_spinbox.value()
            values['game_1v1_price'] = self.game_1v1_price_spinbox.value()
            if self.device.device_type.value == "ps5_regular":
                values['game_2v2_price'] = self.game_2v2_price_spinbox.value()
        else:
            values['price_per_unit'] = self.price_spinbox.value()

        return values

class DeviceWidget(QWidget):
    """Widget for displaying and controlling a single device."""
    
    device_deleted = Signal(str)  # Emitted when device is deleted
    device_updated = Signal()     # Emitted when device is updated
    
    def __init__(self, device: Device, parent=None):
        super().__init__(parent)
        self.device = device
        self.setup_ui()
        self.setup_timer()
        
    def setup_ui(self):
        """Set up the widget UI."""
        # Main frame
        self.setFrameStyle(QFrame.StyledPanel)
        # Adjust height based on device type - babyfoot needs more space for sell coin button
        if self.device.pricing_mode == PricingMode.PER_GAME:
            self.setMinimumSize(290, 280)
            self.setMaximumSize(330, 320)
        else:
            self.setMinimumSize(290, 260)
            self.setMaximumSize(330, 300)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 6, 6, 6)
        layout.setSpacing(4)

        # Device image
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMaximumHeight(65)
        self.image_label.setMinimumHeight(65)
        self.setup_device_image()
        layout.addWidget(self.image_label)
        
        # Header with device name and controls
        header_layout = QHBoxLayout()
        
        self.name_label = QLabel(self.device.name)
        name_font = QFont()
        name_font.setPointSize(12)
        name_font.setBold(True)
        self.name_label.setFont(name_font)
        
        # Edit and delete buttons
        self.edit_button = QPushButton("✏️")
        self.edit_button.setMaximumSize(30, 30)
        self.edit_button.clicked.connect(self.edit_device)
        
        self.delete_button = QPushButton("🗑️")
        self.delete_button.setMaximumSize(30, 30)
        self.delete_button.clicked.connect(self.delete_device)
        
        header_layout.addWidget(self.name_label)
        header_layout.addStretch()
        header_layout.addWidget(self.edit_button)
        header_layout.addWidget(self.delete_button)
        
        layout.addLayout(header_layout)
        
        # Status and pricing info
        self.status_label = QLabel()
        self.price_label = QLabel()
        self.timer_label = QLabel()
        self.cost_label = QLabel()
        
        layout.addWidget(self.status_label)
        layout.addWidget(self.price_label)
        layout.addWidget(self.timer_label)
        layout.addWidget(self.cost_label)
        
        # Control buttons - compact layout
        button_widget = QWidget()
        button_grid = QGridLayout(button_widget)
        button_grid.setSpacing(2)
        button_grid.setContentsMargins(0, 0, 0, 0)

        self.start_button = QPushButton("Start")
        self.start_button.setProperty("class", "primary")
        self.start_button.setMaximumHeight(22)
        self.start_button.setMinimumHeight(22)
        self.start_button.clicked.connect(self.start_session)

        self.pause_button = QPushButton("Pause")
        self.pause_button.setProperty("class", "warning")
        self.pause_button.setMaximumHeight(22)
        self.pause_button.setMinimumHeight(22)
        self.pause_button.clicked.connect(self.pause_session)

        self.end_button = QPushButton("End")
        self.end_button.setProperty("class", "danger")
        self.end_button.setMaximumHeight(22)
        self.end_button.setMinimumHeight(22)
        self.end_button.clicked.connect(self.end_session)

        # Arrange buttons in a single row
        button_grid.addWidget(self.start_button, 0, 0)
        button_grid.addWidget(self.pause_button, 0, 1)
        button_grid.addWidget(self.end_button, 0, 2)

        layout.addWidget(button_widget)

        # Add spacing after main buttons
        layout.addSpacing(5)

        # Special controls for babyfoot - add as separate widget below main buttons
        if self.device.pricing_mode == PricingMode.PER_GAME:
            # Add some spacing
            layout.addSpacing(12)

            self.sell_coin_button = QPushButton("💰 Sell Coins")
            self.sell_coin_button.setProperty("class", "info")
            self.sell_coin_button.setMaximumHeight(30)
            self.sell_coin_button.setMinimumHeight(30)
            self.sell_coin_button.clicked.connect(self.sell_coin)
            # Add as separate widget below the main button grid
            layout.addWidget(self.sell_coin_button)

            # Add bottom spacing to prevent clipping
            layout.addSpacing(12)
        
        # Update display
        self.update_display()

    def setup_device_image(self):
        """Set up the device image based on device type or custom image."""
        # Get the directory where this file is located
        current_dir = os.path.dirname(os.path.dirname(__file__))
        images_dir = os.path.join(current_dir, "images")

        # Check if device has a custom image first
        if hasattr(self.device, 'custom_image') and self.device.custom_image:
            custom_image_path = os.path.join(images_dir, self.device.custom_image)
            if os.path.exists(custom_image_path):
                pixmap = QPixmap(custom_image_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(60, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.image_label.setPixmap(scaled_pixmap)
                    return

        # Map device types to image files (fallback)
        image_map = {
            "ps5_regular": "ps5.png",
            "ps5_vip": "ps5pro.png",
            "arcade": "arcade.png",
            "babyfoot": "babyfoot.png",  # Updated to use new babyfoot image
            "pool": "pool.png",  # Updated to use new pool image
            "other": "arcade.png"  # Default to arcade for other devices
        }

        device_type = self.device.device_type.value
        image_file = image_map.get(device_type, "arcade.png")
        image_path = os.path.join(images_dir, image_file)

        if os.path.exists(image_path):
            pixmap = QPixmap(image_path)
            # Scale the image to fit the label while maintaining aspect ratio
            scaled_pixmap = pixmap.scaled(60, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.image_label.setPixmap(scaled_pixmap)
        else:
            # Fallback to text if image not found
            self.image_label.setText(f"🎮\n{self.device.device_type.value.replace('_', ' ').title()}")
            self.image_label.setStyleSheet("color: #3498db; font-weight: bold; font-size: 10px;")

    def setup_timer(self):
        """Set up timer for regular updates."""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # Update every second
        
    def update_display(self):
        """Update the display with current device state."""
        # Status
        if self.device.current_session:
            state = self.device.current_session.state
            if state == SessionState.ACTIVE:
                self.status_label.setText("🟢 Active")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
            elif state == SessionState.PAUSED:
                self.status_label.setText("🟡 Paused")
                self.status_label.setStyleSheet("color: orange; font-weight: bold;")
        else:
            self.status_label.setText("⚪ Idle")
            self.status_label.setStyleSheet("color: gray; font-weight: bold;")
            
        # Pricing info
        if self.device.pricing_mode == PricingMode.HOURLY:
            self.price_label.setText(f"💰 {self.device.price_per_unit:.2f} DZD/hour")
        else:
            self.price_label.setText(f"💰 {self.device.price_per_unit:.2f} DZD/game")
            
        # Timer and cost
        if self.device.current_session:
            session = self.device.current_session
            duration = session.get_duration()
            hours = int(duration.total_seconds() // 3600)
            minutes = int((duration.total_seconds() % 3600) // 60)
            seconds = int(duration.total_seconds() % 60)

            if self.device.pricing_mode == PricingMode.HOURLY:
                timer_text = f"⏱️ {hours:02d}:{minutes:02d}:{seconds:02d}"
                # Add remaining time if booked
                if session.booked_minutes:
                    remaining = session.get_remaining_time()
                    remaining_minutes = int(remaining.total_seconds() // 60)
                    remaining_seconds = int(remaining.total_seconds() % 60)
                    if remaining_minutes > 0 or remaining_seconds > 0:
                        timer_text += f" | ⏰ -{remaining_minutes:02d}:{remaining_seconds:02d}"
                    else:
                        timer_text += " | ⏰ TIME UP!"
                self.timer_label.setText(timer_text)
            else:
                games = session.games_played
                self.timer_label.setText(f"🎮 Games: {games}")

            cost = self.device.get_current_cost()
            self.cost_label.setText(f"💵 Current: {cost:.2f} DZD")
        else:
            self.timer_label.setText("⏱️ --:--:--")
            self.cost_label.setText("💵 Current: 0.00 DZD")
            
        # Button states
        has_session = self.device.current_session is not None
        is_active = has_session and self.device.current_session.state == SessionState.ACTIVE
        is_paused = has_session and self.device.current_session.state == SessionState.PAUSED
        
        self.start_button.setEnabled(not has_session)
        self.pause_button.setEnabled(is_active or is_paused)  # Enable for both active and paused states
        self.end_button.setEnabled(has_session)
        
        if self.device.pricing_mode == PricingMode.PER_GAME:
            self.sell_coin_button.setEnabled(has_session)
            
        # Update pause button text
        if is_paused:
            self.pause_button.setText("Resume")
        else:
            self.pause_button.setText("Pause")

        # Check for time up notification (hourly sessions only)
        if (has_session and self.device.pricing_mode == PricingMode.HOURLY and
            self.device.current_session.is_time_up() and
            not hasattr(self, '_time_up_notified')):
            self._time_up_notified = True
            self.show_time_up_notification()
            
    def start_session(self):
        """Start a new session."""
        try:
            # For hourly devices, show session type dialog
            if self.device.pricing_mode == PricingMode.HOURLY:
                from .ps5_device_widget import SessionTypeDialog
                dialog = SessionTypeDialog(self.device, self)
                if dialog.exec_() == QDialog.Accepted:
                    config = dialog.get_session_config()
                    if config['type'] == 'open':
                        self.device.start_session()  # Open session
                    elif config['type'] == 'timed':
                        self.device.start_session(booked_minutes=config['booked_minutes'])  # Timed session
                    self.device_updated.emit()
            else:
                # For per-game devices, start normally
                self.device.start_session()
                self.device_updated.emit()
        except ValueError as e:
            QMessageBox.warning(self, "Error", str(e))
            
    def pause_session(self):
        """Pause or resume the current session."""
        if self.device.current_session:
            current_state = self.device.current_session.state

            if current_state == SessionState.ACTIVE:
                self.device.pause_session()
            elif current_state == SessionState.PAUSED:
                self.device.resume_session()

            # Emit signal and force immediate display update
            self.device_updated.emit()
            self.update_display()
            
    def end_session(self):
        """End the current session."""
        if self.device.current_session:
            cost = self.device.get_current_cost()
            reply = QMessageBox.question(
                self, 'End Session',
                f'End session for {self.device.name}?\nTotal cost: {cost:.2f} DZD',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                self.device.end_session()
                # Reset notification flag
                if hasattr(self, '_time_up_notified'):
                    delattr(self, '_time_up_notified')
                self.device_updated.emit()

    def show_time_up_notification(self):
        """Show Windows notification when booked time is up."""
        if self.device.current_session and self.device.current_session.booked_minutes:
            from ..utils.notifications import show_time_up_notification

            booked_time = self.device.current_session.booked_minutes
            current_cost = self.device.get_current_cost()

            # Try Windows toast notification first
            toast_success = show_time_up_notification(
                self.device.name,
                booked_time,
                current_cost
            )

            # Always show dialog as well for interaction
            hours = booked_time // 60
            minutes = booked_time % 60

            time_str = ""
            if hours > 0:
                time_str = f"{hours} hour{'s' if hours > 1 else ''}"
                if minutes > 0:
                    time_str += f" and {minutes} minute{'s' if minutes > 1 else ''}"
            else:
                time_str = f"{minutes} minute{'s' if minutes > 1 else ''}"

            # Show dialog for user interaction
            reply = QMessageBox.question(
                self, '⏰ Time Up!',
                f'The booked time ({time_str}) for {self.device.name} has elapsed!\n\n'
                f'Current cost: {current_cost:.2f} DZD\n\n'
                f'Do you want to end the session now?',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.device.end_session()
                if hasattr(self, '_time_up_notified'):
                    delattr(self, '_time_up_notified')
                self.device_updated.emit()

    def sell_coin(self):
        """Sell coins (add games for babyfoot)."""
        if self.device.current_session and self.device.pricing_mode == PricingMode.PER_GAME:
            # Ask how many coins to sell
            coins, ok = QInputDialog.getInt(
                self,
                "Sell Coins",
                "How many coins to sell?",
                value=1,  # Default value
                min=1,    # Minimum value
                max=20,   # Maximum value
                step=1    # Step size
            )

            if ok and coins > 0:
                # Add the specified number of games
                for _ in range(coins):
                    self.device.add_game()
                self.device_updated.emit()

                # Show confirmation
                QMessageBox.information(
                    self,
                    "Coins Sold",
                    f"Successfully sold {coins} coin{'s' if coins > 1 else ''} for {self.device.name}!"
                )
            
    def edit_device(self):
        """Edit device properties."""
        dialog = EditDeviceDialog(self.device, self)
        if dialog.exec_() == QDialog.Accepted:
            values = dialog.get_values()
            if values['name']:
                self.device.name = values['name']
                self.device.price_per_unit = values['price_per_unit']
                self.name_label.setText(self.device.name)
                self.device_updated.emit()
                
    def delete_device(self):
        """Delete the device."""
        reply = QMessageBox.question(
            self, 'Delete Device',
            f'Are you sure you want to delete "{self.device.name}"?\nThis action cannot be undone.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.device.current_session:
                # End current session before deleting
                self.device.end_session()
            self.device.is_active = False
            self.device_deleted.emit(self.device.id)
            
    def setFrameStyle(self, style):
        """Set frame style for the widget."""
        self.setStyleSheet("""
            DeviceWidget {
                background-color: white;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                margin: 5px;
            }

            DeviceWidget:hover {
                border-color: #2c3e50;
            }
        """)
