{"app": {"title": "Ωmega GameZone", "version": "Version", "exit_confirmation": "Exit Application", "exit_message": "Are you sure you want to exit?\nAll data will be saved automatically."}, "navigation": {"panels": "Panels", "ps5": "PS5", "arcade": "Arcade", "babyfoot": "Baby<PERSON>", "pool": "Pool", "products": "Products", "other": "Other", "statistics": "Statistics", "settings": "Settings"}, "buttons": {"save": "Save", "cancel": "Cancel", "reset": "Reset to Defaults", "ok": "OK", "yes": "Yes", "no": "No", "start": "Start", "stop": "Stop", "pause": "Pause", "resume": "Resume", "add": "Add", "edit": "Edit", "delete": "Delete", "export": "Export", "refresh": "Refresh"}, "settings": {"title": "Settings", "general": "General", "display": "Display", "business": "Business", "app_settings": "Application Settings", "shop_name": "Shop Name", "shop_name_placeholder": "Enter your shop name", "language": "Language", "window_settings": "Window Settings", "default_width": "<PERSON><PERSON><PERSON>", "default_height": "Default Height", "minimum_width": "Minimum Width", "minimum_height": "Minimum Height", "business_settings": "Business Settings", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_placeholder": "e.g., USD, EUR, DZD", "tax_rate": "Tax Rate", "auto_save_interval": "Auto-save Interval", "settings_saved": "Settings Saved", "settings_saved_message": "Settings have been saved successfully.\nSome changes may require restarting the application.", "reset_settings": "Reset Settings", "reset_confirmation": "Are you sure you want to reset all settings to their default values?", "settings_reset": "Settings Reset", "settings_reset_message": "All settings have been reset to default values.", "invalid_input": "Invalid Input", "shop_name_empty": "Shop name cannot be empty.", "error": "Error", "save_error": "Failed to save settings"}, "devices": {"status": "Status", "available": "Available", "occupied": "Occupied", "maintenance": "Maintenance", "time_remaining": "Time Remaining", "session_time": "Session Time", "total_revenue": "Total Revenue", "hourly_rate": "Hourly Rate", "game_1v1_price": "1v1 Game Price", "game_2v2_price": "2v2 Game Price", "start_session": "Start Session", "end_session": "End Session", "add_time": "Add Time", "minutes": "minutes", "hours": "hours", "device_updated": "<PERSON><PERSON>"}, "statistics": {"title": "Statistics", "overview": "Overview", "devices": "Devices", "products": "Products", "revenue": "Revenue", "total_revenue": "Total Revenue", "total_sessions": "Total Sessions", "active_devices": "Active Devices", "average_session": "Average Session", "daily_revenue": "Daily Revenue", "monthly_revenue": "Monthly Revenue", "total_sales": "Total Sales", "total_products": "Total Products", "drinks_count": "Drinks", "chips_count": "Chips", "snacks_count": "Snacks", "performance": "Performance", "category_revenue": "Category Revenue", "device_name": "Device Name", "sessions": "Sessions", "revenue_generated": "Revenue Generated", "category": "Category", "sales": "Sales"}, "products": {"title": "Products", "add_product": "Add Product", "edit_product": "Edit Product", "product_name": "Product Name", "price": "Price", "category": "Category", "brand": "Brand", "stock": "Stock", "drinks": "Drinks", "chips": "Chips", "snacks": "Snacks", "product_added": "Product Added", "product_updated": "Product Updated", "product_deleted": "Product Deleted", "delete_confirmation": "Are you sure you want to delete this product?", "invalid_price": "Please enter a valid price.", "invalid_stock": "Please enter a valid stock quantity."}, "history": {"title": "History", "device": "<PERSON><PERSON>", "start_time": "Start Time", "end_time": "End Time", "duration": "Duration", "revenue": "Revenue", "games_played": "Games Played", "export_history": "Export History", "export_error": "Export Error", "export_failed": "Failed to export history"}, "common": {"loading": "Loading...", "no_data": "No data available", "search": "Search", "filter": "Filter", "date": "Date", "time": "Time", "total": "Total", "average": "Average", "minimum": "Minimum", "maximum": "Maximum"}}