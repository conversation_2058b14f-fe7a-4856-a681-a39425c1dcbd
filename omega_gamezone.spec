# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Define the main script
main_script = 'main.py'

# Define data files to include
added_files = [
    ('src/images', 'src/images'),
    ('src/translations', 'src/translations'),
    ('config.json', '.'),
    ('README.md', '.'),
    ('requirements.txt', '.'),
]

# Hidden imports for AI libraries
hiddenimports = [
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_agg',
    'matplotlib.figure',
    'matplotlib.pyplot',
    'numpy',
    'pandas',
    'sklearn',
    'sklearn.ensemble',
    'sklearn.neighbors',
    'sklearn.linear_model',
    'pulp',
    'experta',
    'pyod',
    'pyod.models.iforest',
    'pyod.models.lof',
    'pyod.models.ocsvm',
    'pyod.models.combination',
    'PySide2.QtCore',
    'PySide2.QtGui',
    'PySide2.QtWidgets',
]

a = Analysis(
    [main_script],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='Omega GameZone',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # Set to False for windowed app
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='src/images/ps5.png',  # Use PS5 image as icon
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Omega GameZone',
)
