"""
Test AI Recommendations with Fake Data
"""

import sys
import os
sys.path.append('src')

from utils.fake_data_generator import FakeDataGenerator
from ai.ai_coordinator import AICoordinator
import json


def test_ai_recommendations():
    """Test AI recommendations with different scenarios."""
    
    print("🤖 Testing Advanced AI Recommendations System")
    print("=" * 60)
    
    # Initialize components
    data_generator = FakeDataGenerator()
    ai_coordinator = AICoordinator()
    
    # Test scenarios
    scenarios = ['normal', 'busy', 'slow', 'unbalanced', 'anomaly']
    
    for scenario_type in scenarios:
        print(f"\n📊 TESTING SCENARIO: {scenario_type.upper()}")
        print("-" * 40)
        
        # Generate fake data
        scenario_data = data_generator.generate_business_scenario(scenario_type)
        business_data = scenario_data['business_data']
        products_data = scenario_data['products_data']
        
        print(f"📝 Scenario: {scenario_data['scenario_description']}")
        print(f"💰 Revenue: {business_data['total_revenue']:.2f} DZD")
        print(f"🎮 Sessions: {business_data['total_sessions']}")
        print(f"🛒 Product Sales: {products_data['total_sales']}")
        
        # Get AI analysis
        try:
            analysis = ai_coordinator.get_comprehensive_analysis(
                business_data, products_data
            )
            
            # Display results
            print("\n🎯 AI ANALYSIS RESULTS:")
            
            # Executive Summary
            exec_summary = analysis.get('executive_summary', {})
            print(f"   Business Health: {exec_summary.get('business_health', 'unknown').title()}")
            print(f"   Revenue Outlook: {exec_summary.get('revenue_outlook', 'unknown').title()}")
            print(f"   Risk Level: {exec_summary.get('risk_level', 'unknown').title()}")
            
            # Pricing Recommendations
            pricing = analysis.get('pricing_analysis', {})
            price_changes = pricing.get('price_changes', {})
            significant_changes = [
                (device, change) for device, change in price_changes.items()
                if abs(change.get('change_percent', 0)) > 5
            ]
            
            if significant_changes:
                print(f"\n💰 PRICING RECOMMENDATIONS ({len(significant_changes)} changes):")
                for device, change in significant_changes[:3]:
                    pct = change.get('change_percent', 0)
                    arrow = "📈" if pct > 0 else "📉"
                    print(f"   {arrow} {device}: {change['current']:.2f} → {change['optimized']:.2f} DZD ({pct:+.1f}%)")
            
            # Promotions
            promotions = analysis.get('promotion_recommendations', {}).get('promotions', [])
            if promotions:
                print(f"\n🎯 PROMOTIONS ({len(promotions)} opportunities):")
                for promo in promotions[:3]:
                    print(f"   • {promo.get('promotion', 'N/A')}")
            
            # VIP Customers
            vip_customers = analysis.get('promotion_recommendations', {}).get('vip_customers', [])
            if vip_customers:
                print(f"\n👑 VIP CUSTOMERS: {len(vip_customers)} detected")
            
            # Anomalies
            anomaly_detection = analysis.get('anomaly_detection', {})
            if anomaly_detection.get('is_anomaly'):
                anomalies = anomaly_detection.get('anomalies_detected', [])
                print(f"\n⚠️ ANOMALIES DETECTED ({len(anomalies)}):")
                for anomaly in anomalies[:2]:
                    print(f"   • {anomaly.get('description', 'Unknown')}")
            else:
                print(f"\n✅ NO ANOMALIES (Score: {anomaly_detection.get('consensus_score', 0):.2f})")
            
            # Action Items
            action_items = analysis.get('action_items', [])
            if action_items:
                print(f"\n📋 TOP ACTION ITEMS ({len(action_items)}):")
                priority_emojis = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}
                for item in action_items[:3]:
                    emoji = priority_emojis.get(item.get('priority', 'low'), '⚪')
                    print(f"   {emoji} {item.get('title', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ AI Analysis Failed: {e}")
        
        print("\n" + "=" * 60)
    
    print("\n🎉 AI Recommendations Testing Complete!")


def test_individual_modules():
    """Test individual AI modules separately."""
    
    print("\n🔧 TESTING INDIVIDUAL AI MODULES")
    print("=" * 60)
    
    data_generator = FakeDataGenerator()
    ai_coordinator = AICoordinator()
    
    # Generate test data
    scenario_data = data_generator.generate_business_scenario('normal')
    business_data = scenario_data['business_data']
    products_data = scenario_data['products_data']
    
    print("📊 Test Data Generated:")
    print(f"   Revenue: {business_data['total_revenue']:.2f} DZD")
    print(f"   Sessions: {business_data['total_sessions']}")
    print(f"   Products: {products_data['total_products']}")
    
    # Test Smart Pricing
    print("\n💰 TESTING SMART PRICING (PuLP):")
    try:
        pricing_result = ai_coordinator.get_pricing_recommendations_only(business_data)
        optimized_prices = pricing_result.get('optimized_prices', {})
        print(f"   Optimized Prices: {len(optimized_prices)} devices")
        for device, price in list(optimized_prices.items())[:3]:
            print(f"   • {device}: {price:.2f} DZD")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test Promotions Engine
    print("\n🎯 TESTING PROMOTIONS ENGINE (Experta):")
    try:
        promo_result = ai_coordinator.get_promotions_only(business_data, products_data)
        promotions = promo_result.get('promotions', [])
        print(f"   Promotions Generated: {len(promotions)}")
        for promo in promotions[:3]:
            print(f"   • {promo.get('type', 'unknown')}: {promo.get('promotion', 'N/A')}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test Anomaly Detection
    print("\n⚠️ TESTING ANOMALY DETECTION (PyOD):")
    try:
        anomaly_result = ai_coordinator.check_anomalies_only(business_data, products_data)
        is_anomaly = anomaly_result.get('is_anomaly', False)
        score = anomaly_result.get('consensus_score', 0)
        print(f"   Anomaly Detected: {is_anomaly}")
        print(f"   Consensus Score: {score:.3f}")
        
        if is_anomaly:
            anomalies = anomaly_result.get('anomalies_detected', [])
            print(f"   Specific Anomalies: {len(anomalies)}")
    except Exception as e:
        print(f"   ❌ Error: {e}")


def generate_test_report():
    """Generate a comprehensive test report."""
    
    print("\n📋 GENERATING COMPREHENSIVE TEST REPORT")
    print("=" * 60)
    
    data_generator = FakeDataGenerator()
    ai_coordinator = AICoordinator()
    
    # Test all scenarios and collect results
    results = {}
    
    for scenario_type in ['normal', 'busy', 'slow', 'unbalanced', 'anomaly']:
        print(f"Testing {scenario_type}...")
        
        scenario_data = data_generator.generate_business_scenario(scenario_type)
        business_data = scenario_data['business_data']
        products_data = scenario_data['products_data']
        
        try:
            analysis = ai_coordinator.get_comprehensive_analysis(business_data, products_data)
            
            # Extract key metrics
            results[scenario_type] = {
                'scenario_description': scenario_data['scenario_description'],
                'business_health': analysis.get('executive_summary', {}).get('business_health', 'unknown'),
                'revenue': business_data['total_revenue'],
                'sessions': business_data['total_sessions'],
                'pricing_changes': len([c for c in analysis.get('pricing_analysis', {}).get('price_changes', {}).values() 
                                      if abs(c.get('change_percent', 0)) > 5]),
                'promotions_count': len(analysis.get('promotion_recommendations', {}).get('promotions', [])),
                'vip_customers': len(analysis.get('promotion_recommendations', {}).get('vip_customers', [])),
                'anomalies_detected': analysis.get('anomaly_detection', {}).get('is_anomaly', False),
                'action_items': len(analysis.get('action_items', []))
            }
            
        except Exception as e:
            results[scenario_type] = {'error': str(e)}
    
    # Generate summary report
    print("\n📊 TEST RESULTS SUMMARY:")
    print("-" * 40)
    
    for scenario, result in results.items():
        if 'error' in result:
            print(f"{scenario.upper()}: ❌ Failed - {result['error']}")
        else:
            health_emoji = {'excellent': '🟢', 'good': '🔵', 'fair': '🟡', 'needs_attention': '🔴'}.get(result['business_health'], '⚪')
            anomaly_emoji = '⚠️' if result['anomalies_detected'] else '✅'
            
            print(f"{scenario.upper()}: {health_emoji} {result['business_health'].title()}")
            print(f"   Revenue: {result['revenue']:.0f} DZD | Sessions: {result['sessions']}")
            print(f"   Pricing: {result['pricing_changes']} changes | Promos: {result['promotions_count']}")
            print(f"   VIPs: {result['vip_customers']} | Anomalies: {anomaly_emoji} | Actions: {result['action_items']}")
            print()
    
    return results


if __name__ == "__main__":
    print("🚀 Starting AI Recommendations Testing Suite")
    print("=" * 60)
    
    # Run all tests
    test_ai_recommendations()
    test_individual_modules()
    results = generate_test_report()
    
    print("\n✅ All tests completed!")
    print("Check the output above to see how the AI recommendations work.")
