"""
Fake Data Generator for testing AI recommendations
"""

import random
import json
from datetime import datetime, timedelta
from typing import Dict, List


class FakeDataGenerator:
    """Generate realistic fake data for testing AI recommendations."""
    
    def __init__(self):
        self.device_types = ['ps5_1v1', 'ps5_2v2', 'arcade', 'pool', 'babyfoot', 'other']
        self.product_categories = ['drinks', 'chips', 'snacks']
        self.product_names = {
            'drinks': ['Coca Cola', 'Pepsi', 'Red Bull', 'Monster Energy', 'Water', 'Orange Juice'],
            'chips': ['Lays Classic', 'Doritos', 'Pringles', 'Cheetos', 'Ruffles'],
            'snacks': ['Snickers', 'KitKat', 'Oreos', 'Haribo', 'Twix', 'Mars']
        }
        self.brands = {
            'drinks': ['Coca-Cola', 'PepsiCo', 'Red Bull', 'Monster', 'Nestle', 'Tropicana'],
            'chips': ['Lays', 'Doritos', 'Pringles', 'Cheetos', 'Ruffles'],
            'snacks': ['Mars', 'Nestle', 'Mondelez', 'Haribo', 'Ferrero']
        }
        
    def generate_business_scenario(self, scenario_type: str = 'normal') -> Dict:
        """
        Generate different business scenarios for testing.
        
        Args:
            scenario_type: 'normal', 'busy', 'slow', 'unbalanced', 'anomaly'
        """
        if scenario_type == 'normal':
            return self._generate_normal_scenario()
        elif scenario_type == 'busy':
            return self._generate_busy_scenario()
        elif scenario_type == 'slow':
            return self._generate_slow_scenario()
        elif scenario_type == 'unbalanced':
            return self._generate_unbalanced_scenario()
        elif scenario_type == 'anomaly':
            return self._generate_anomaly_scenario()
        else:
            return self._generate_normal_scenario()
    
    def _generate_normal_scenario(self) -> Dict:
        """Generate normal business day scenario."""
        # Normal day: 15-25 sessions, balanced device usage
        total_sessions = random.randint(15, 25)
        total_revenue = random.uniform(800, 1500)
        
        # Balanced device distribution
        sessions_by_type = self._distribute_sessions(total_sessions, 'balanced')
        
        # Normal hourly pattern (peak in afternoon/evening)
        hourly_usage = self._generate_hourly_pattern('normal')
        
        # Generate products data
        products_data = self._generate_products_data('normal')
        
        return {
            'business_data': {
                'total_revenue': total_revenue,
                'total_sessions': total_sessions,
                'sessions_by_type': sessions_by_type,
                'hourly_usage': hourly_usage,
                'revenue_by_type': self._calculate_revenue_by_type(sessions_by_type, total_revenue),
                'device_usage': sessions_by_type,
                'device_revenue': self._calculate_device_revenue(sessions_by_type, total_revenue)
            },
            'products_data': products_data,
            'scenario_description': 'Normal business day with balanced operations'
        }
    
    def _generate_busy_scenario(self) -> Dict:
        """Generate busy day scenario."""
        # Busy day: 35-50 sessions, high revenue
        total_sessions = random.randint(35, 50)
        total_revenue = random.uniform(2000, 3500)
        
        # PS5 dominated usage
        sessions_by_type = self._distribute_sessions(total_sessions, 'ps5_heavy')
        
        # Peak hours extended
        hourly_usage = self._generate_hourly_pattern('busy')
        
        # High product sales
        products_data = self._generate_products_data('busy')
        
        return {
            'business_data': {
                'total_revenue': total_revenue,
                'total_sessions': total_sessions,
                'sessions_by_type': sessions_by_type,
                'hourly_usage': hourly_usage,
                'revenue_by_type': self._calculate_revenue_by_type(sessions_by_type, total_revenue),
                'device_usage': sessions_by_type,
                'device_revenue': self._calculate_device_revenue(sessions_by_type, total_revenue)
            },
            'products_data': products_data,
            'scenario_description': 'Busy day with high demand and revenue'
        }
    
    def _generate_slow_scenario(self) -> Dict:
        """Generate slow day scenario."""
        # Slow day: 3-8 sessions, low revenue
        total_sessions = random.randint(3, 8)
        total_revenue = random.uniform(150, 400)
        
        # Random low usage
        sessions_by_type = self._distribute_sessions(total_sessions, 'random')
        
        # Low activity throughout day
        hourly_usage = self._generate_hourly_pattern('slow')
        
        # Low product sales
        products_data = self._generate_products_data('slow')
        
        return {
            'business_data': {
                'total_revenue': total_revenue,
                'total_sessions': total_sessions,
                'sessions_by_type': sessions_by_type,
                'hourly_usage': hourly_usage,
                'revenue_by_type': self._calculate_revenue_by_type(sessions_by_type, total_revenue),
                'device_usage': sessions_by_type,
                'device_revenue': self._calculate_device_revenue(sessions_by_type, total_revenue)
            },
            'products_data': products_data,
            'scenario_description': 'Slow day with low activity and revenue'
        }
    
    def _generate_unbalanced_scenario(self) -> Dict:
        """Generate unbalanced device usage scenario."""
        total_sessions = random.randint(18, 28)
        total_revenue = random.uniform(900, 1600)
        
        # One device dominates (70%+ usage)
        dominant_device = random.choice(self.device_types)
        sessions_by_type = self._distribute_sessions(total_sessions, 'unbalanced', dominant_device)
        
        hourly_usage = self._generate_hourly_pattern('normal')
        products_data = self._generate_products_data('normal')
        
        return {
            'business_data': {
                'total_revenue': total_revenue,
                'total_sessions': total_sessions,
                'sessions_by_type': sessions_by_type,
                'hourly_usage': hourly_usage,
                'revenue_by_type': self._calculate_revenue_by_type(sessions_by_type, total_revenue),
                'device_usage': sessions_by_type,
                'device_revenue': self._calculate_device_revenue(sessions_by_type, total_revenue)
            },
            'products_data': products_data,
            'scenario_description': f'Unbalanced day with {dominant_device} dominating usage'
        }
    
    def _generate_anomaly_scenario(self) -> Dict:
        """Generate anomalous scenario."""
        # Anomaly: Very high sessions but low revenue OR very high revenue with few sessions
        anomaly_type = random.choice(['high_sessions_low_revenue', 'low_sessions_high_revenue'])
        
        if anomaly_type == 'high_sessions_low_revenue':
            total_sessions = random.randint(45, 60)  # Very high
            total_revenue = random.uniform(200, 500)  # Very low
            description = 'Anomaly: High session count but unusually low revenue'
        else:
            total_sessions = random.randint(3, 8)  # Very low
            total_revenue = random.uniform(2500, 4000)  # Very high
            description = 'Anomaly: Low session count but unusually high revenue'
        
        sessions_by_type = self._distribute_sessions(total_sessions, 'random')
        hourly_usage = self._generate_hourly_pattern('anomaly')
        products_data = self._generate_products_data('anomaly')
        
        return {
            'business_data': {
                'total_revenue': total_revenue,
                'total_sessions': total_sessions,
                'sessions_by_type': sessions_by_type,
                'hourly_usage': hourly_usage,
                'revenue_by_type': self._calculate_revenue_by_type(sessions_by_type, total_revenue),
                'device_usage': sessions_by_type,
                'device_revenue': self._calculate_device_revenue(sessions_by_type, total_revenue)
            },
            'products_data': products_data,
            'scenario_description': description
        }
    
    def _distribute_sessions(self, total_sessions: int, pattern: str, dominant_device: str = None) -> Dict[str, int]:
        """Distribute sessions across device types based on pattern."""
        sessions = {device: 0 for device in self.device_types}
        
        if pattern == 'balanced':
            # Balanced distribution with PS5 slightly favored
            weights = [0.25, 0.20, 0.20, 0.15, 0.12, 0.08]  # ps5_1v1, ps5_2v2, arcade, pool, babyfoot, other
        elif pattern == 'ps5_heavy':
            # PS5 dominated
            weights = [0.40, 0.30, 0.12, 0.08, 0.06, 0.04]
        elif pattern == 'unbalanced':
            # One device dominates
            weights = [0.1] * len(self.device_types)
            dominant_idx = self.device_types.index(dominant_device)
            weights[dominant_idx] = 0.7
            # Normalize remaining
            remaining = 0.3 / (len(self.device_types) - 1)
            for i in range(len(weights)):
                if i != dominant_idx:
                    weights[i] = remaining
        else:  # random
            weights = [random.random() for _ in self.device_types]
            total_weight = sum(weights)
            weights = [w / total_weight for w in weights]
        
        # Distribute sessions
        remaining_sessions = total_sessions
        for i, device in enumerate(self.device_types[:-1]):
            device_sessions = int(total_sessions * weights[i])
            sessions[device] = device_sessions
            remaining_sessions -= device_sessions
        
        # Give remaining to last device
        sessions[self.device_types[-1]] = remaining_sessions
        
        return sessions
    
    def _generate_hourly_pattern(self, pattern_type: str) -> Dict[int, int]:
        """Generate hourly usage pattern."""
        hourly = {hour: 0 for hour in range(24)}
        
        if pattern_type == 'normal':
            # Peak hours: 14-22 (2 PM to 10 PM)
            peak_hours = list(range(14, 23))
            for hour in peak_hours:
                hourly[hour] = random.randint(2, 6)
            # Off-peak hours
            for hour in range(24):
                if hour not in peak_hours:
                    hourly[hour] = random.randint(0, 2)
                    
        elif pattern_type == 'busy':
            # Extended peak hours with higher intensity
            peak_hours = list(range(12, 24))
            for hour in peak_hours:
                hourly[hour] = random.randint(4, 8)
            for hour in range(12):
                hourly[hour] = random.randint(0, 3)
                
        elif pattern_type == 'slow':
            # Low activity all day
            for hour in range(24):
                hourly[hour] = random.randint(0, 1)
                
        elif pattern_type == 'anomaly':
            # Unusual pattern - high activity at odd hours
            odd_hours = [3, 4, 5, 25, 26]  # Early morning
            for hour in range(24):
                if hour in [3, 4, 5]:  # Early morning anomaly
                    hourly[hour] = random.randint(5, 10)
                else:
                    hourly[hour] = random.randint(0, 2)
        
        return hourly
    
    def _generate_products_data(self, scenario: str) -> Dict:
        """Generate products and sales data."""
        # Generate products
        all_products = []
        all_sales = []
        
        for category in self.product_categories:
            product_count = random.randint(3, 8)
            for i in range(product_count):
                product_name = random.choice(self.product_names[category])
                brand = random.choice(self.brands[category])
                price = self._get_product_price(category)
                
                product = {
                    'name': f"{product_name} {i+1}" if i > 0 else product_name,
                    'category': category,
                    'price': price,
                    'brand': brand
                }
                all_products.append(product)
        
        # Generate sales based on scenario
        if scenario == 'busy':
            sales_count = random.randint(25, 45)
        elif scenario == 'slow':
            sales_count = random.randint(2, 8)
        elif scenario == 'anomaly':
            sales_count = random.randint(40, 80)  # Unusually high
        else:  # normal
            sales_count = random.randint(10, 25)
        
        # Generate sales
        for _ in range(sales_count):
            product = random.choice(all_products)
            sale_time = datetime.now() - timedelta(hours=random.randint(0, 24))
            
            sale = {
                'product_name': product['name'],
                'category': product['category'],
                'price': product['price'],
                'brand': product['brand'],
                'timestamp': sale_time,
                'sale_id': f"sale_{sale_time.strftime('%Y%m%d_%H%M%S')}_{random.randint(1000, 9999)}"
            }
            all_sales.append(sale)
        
        # Calculate totals
        total_revenue = sum(sale['price'] for sale in all_sales)
        category_counts = {cat: len([p for p in all_products if p['category'] == cat]) for cat in self.product_categories}
        
        return {
            'total_products': len(all_products),
            'drinks_count': category_counts['drinks'],
            'chips_count': category_counts['chips'],
            'snacks_count': category_counts['snacks'],
            'total_sales': len(all_sales),
            'total_revenue': total_revenue,
            'all_products': all_products,
            'all_sales': all_sales,
            'by_category': {
                'drinks': {'products': [p for p in all_products if p['category'] == 'drinks'], 'sales_history': [s for s in all_sales if s['category'] == 'drinks']},
                'chips': {'products': [p for p in all_products if p['category'] == 'chips'], 'sales_history': [s for s in all_sales if s['category'] == 'chips']},
                'snacks': {'products': [p for p in all_products if p['category'] == 'snacks'], 'sales_history': [s for s in all_sales if s['category'] == 'snacks']}
            }
        }
    
    def _get_product_price(self, category: str) -> float:
        """Get realistic price for product category."""
        price_ranges = {
            'drinks': (50, 150),
            'chips': (80, 200),
            'snacks': (60, 180)
        }
        min_price, max_price = price_ranges[category]
        return round(random.uniform(min_price, max_price), 2)
    
    def _calculate_revenue_by_type(self, sessions_by_type: Dict, total_revenue: float) -> Dict[str, float]:
        """Calculate revenue distribution by device type."""
        total_sessions = sum(sessions_by_type.values()) or 1
        revenue_by_type = {}
        
        for device, sessions in sessions_by_type.items():
            proportion = sessions / total_sessions
            revenue_by_type[device] = total_revenue * proportion
            
        return revenue_by_type
    
    def _calculate_device_revenue(self, sessions_by_type: Dict, total_revenue: float) -> Dict[str, float]:
        """Calculate device revenue (same as revenue_by_type for now)."""
        return self._calculate_revenue_by_type(sessions_by_type, total_revenue)
    
    def save_scenario_to_file(self, scenario_data: Dict, filename: str):
        """Save scenario data to JSON file for testing."""
        # Convert datetime objects to strings for JSON serialization
        def json_serializer(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        with open(filename, 'w') as f:
            json.dump(scenario_data, f, indent=2, default=json_serializer)
        
        print(f"Scenario saved to {filename}")
