{"app": {"title": "Ωmega GameZone", "version": "Version", "exit_confirmation": "<PERSON><PERSON><PERSON> l'Application", "exit_message": "Êtes-vous sûr de vouloir quitter ?\nToutes les données seront sauvegardées automatiquement."}, "navigation": {"panels": "<PERSON>neaux", "ps5": "PS5", "arcade": "Arcade", "babyfoot": "Baby<PERSON>", "pool": "<PERSON><PERSON>", "products": "Produits", "other": "<PERSON><PERSON>", "statistics": "Statistiques", "settings": "Paramètres"}, "buttons": {"save": "Enregistrer", "cancel": "Annuler", "reset": "Réinitialiser", "ok": "OK", "yes": "O<PERSON>", "no": "Non", "start": "<PERSON><PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "pause": "Pause", "resume": "Reprendre", "add": "Ajouter", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "export": "Exporter", "refresh": "Actualiser"}, "settings": {"title": "Paramètres", "general": "Général", "display": "Affichage", "business": "Commerce", "app_settings": "Paramètres de l'Application", "shop_name": "Nom du Magasin", "shop_name_placeholder": "Entrez le nom de votre magasin", "language": "<PERSON><PERSON>", "window_settings": "Paramètres de Fenêtre", "default_width": "Largeur par <PERSON>", "default_height": "Hauteur par D<PERSON>faut", "minimum_width": "Largeur Minimale", "minimum_height": "Hauteur Minimale", "business_settings": "Paramètres Commerciaux", "currency": "<PERSON><PERSON>", "currency_placeholder": "ex: USD, EUR, DZD", "tax_rate": "<PERSON><PERSON>", "auto_save_interval": "Intervalle de Sauvegarde Auto", "settings_saved": "Paramètres Sauvegardés", "settings_saved_message": "Les paramètres ont été sauvegardés avec succès.\nCertains changements peuvent nécessiter un redémarrage de l'application.", "reset_settings": "Réinitialiser les Paramètres", "reset_confirmation": "Êtes-vous sûr de vouloir réinitialiser tous les paramètres à leurs valeurs par défaut ?", "settings_reset": "Paramètres Réinitialisés", "settings_reset_message": "Tous les paramètres ont été réinitialisés aux valeurs par défaut.", "invalid_input": "<PERSON><PERSON><PERSON>", "shop_name_empty": "Le nom du magasin ne peut pas être vide.", "error": "<PERSON><PERSON><PERSON>", "save_error": "Échec de la sauvegarde des paramètres"}, "devices": {"status": "Statut", "available": "Disponible", "occupied": "<PERSON><PERSON><PERSON><PERSON>", "maintenance": "Maintenance", "time_remaining": "<PERSON><PERSON>", "session_time": "Temps de Session", "total_revenue": "<PERSON><PERSON><PERSON>", "hourly_rate": "<PERSON><PERSON><PERSON>", "game_1v1_price": "Prix Jeu 1v1", "game_2v2_price": "Prix Jeu 2v2", "start_session": "<PERSON><PERSON><PERSON><PERSON> Session", "end_session": "Terminer Session", "add_time": "<PERSON><PERSON><PERSON> du Temps", "minutes": "minutes", "hours": "heures", "device_updated": "Appareil Mis à Jour"}, "statistics": {"title": "Statistiques", "overview": "<PERSON><PERSON><PERSON><PERSON>", "devices": "Appareils", "products": "Produits", "revenue": "<PERSON>en<PERSON>", "total_revenue": "<PERSON><PERSON><PERSON>", "total_sessions": "Sessions Totales", "active_devices": "Appareils Actifs", "average_session": "Session Moyenne", "daily_revenue": "<PERSON><PERSON><PERSON> Q<PERSON>tidiens", "monthly_revenue": "<PERSON><PERSON><PERSON>", "total_sales": "Ventes Totales", "total_products": "Produits Totaux", "drinks_count": "<PERSON><PERSON>", "chips_count": "Chips", "snacks_count": "Collations", "performance": "Performance", "category_revenue": "Revenus par Catégorie", "device_name": "Nom de l'Appareil", "sessions": "Sessions", "revenue_generated": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON><PERSON>", "sales": "<PERSON><PERSON><PERSON>"}, "products": {"title": "Produits", "add_product": "Ajouter Produit", "edit_product": "Modifier Produit", "product_name": "Nom du Produit", "price": "Prix", "category": "<PERSON><PERSON><PERSON><PERSON>", "brand": "Marque", "stock": "Stock", "drinks": "<PERSON><PERSON>", "chips": "Chips", "snacks": "Collations", "product_added": "Produit <PERSON>", "product_updated": "Produit Mis à Jour", "product_deleted": "Produit Supprimé", "delete_confirmation": "Êtes-vous sûr de vouloir supprimer ce produit ?", "invalid_price": "Veuillez entrer un prix valide.", "invalid_stock": "Veuillez entrer une quantité de stock valide."}, "history": {"title": "Historique", "device": "Appareil", "start_time": "<PERSON><PERSON>but", "end_time": "<PERSON>ure de Fin", "duration": "<PERSON><PERSON><PERSON>", "revenue": "<PERSON>en<PERSON>", "games_played": "<PERSON><PERSON>", "export_history": "Exporter l'Historique", "export_error": "Erreur d'Exportation", "export_failed": "Échec de l'exportation de l'historique"}, "common": {"loading": "Chargement...", "no_data": "<PERSON><PERSON><PERSON> donnée disponible", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "date": "Date", "time": "<PERSON><PERSON>", "total": "Total", "average": "<PERSON><PERSON><PERSON>", "minimum": "Minimum", "maximum": "Maximum"}}