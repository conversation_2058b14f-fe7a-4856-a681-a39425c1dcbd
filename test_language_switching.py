#!/usr/bin/env python3
"""
Test script to verify language switching functionality.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.translation_manager import get_translation_manager, tr
from utils.config_manager import Confi<PERSON><PERSON><PERSON><PERSON>

def test_language_switching():
    """Test the language switching functionality."""
    print("Testing Ωmega GameZone Language Switching")
    print("=" * 50)
    
    # Initialize config manager
    config_manager = ConfigManager()
    
    # Initialize translation manager
    translation_manager = get_translation_manager(config_manager)
    
    # Test different languages
    languages = ["en", "fr", "ar"]
    test_keys = [
        "navigation.settings",
        "navigation.statistics", 
        "navigation.panels",
        "navigation.ps5",
        "navigation.arcade",
        "navigation.babyfoot",
        "navigation.pool",
        "navigation.products"
    ]
    
    for lang in languages:
        print(f"\n--- Testing {lang.upper()} ---")
        translation_manager.set_language(lang)
        
        for key in test_keys:
            translated = tr(key)
            print(f"{key}: {translated}")
    
    print("\n" + "=" * 50)
    print("Language switching test completed!")

if __name__ == "__main__":
    test_language_switching()
