"""
Data manager for handling persistence and statistics.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict

from .device import Device, DeviceType, Session

class DataManager:
    """Manages data persistence and statistics calculation."""
    
    def __init__(self, data_file: str = "gameshop_data.json"):
        self.data_file = data_file
        self.devices: Dict[str, Device] = {}
        self.products: Dict[str, List[Dict]] = {
            'drinks': [],
            'chips': [],
            'snacks': []
        }
        self.load_data()
        
    def add_device(self, device: Device):
        """Add a device to the manager."""
        self.devices[device.id] = device
        self.save_data()
        
    def remove_device(self, device_id: str):
        """Remove a device from the manager."""
        if device_id in self.devices:
            del self.devices[device_id]
            self.save_data()
            
    def get_device(self, device_id: str) -> Optional[Device]:
        """Get a device by ID."""
        return self.devices.get(device_id)
        
    def get_devices_by_type(self, device_type: DeviceType) -> List[Device]:
        """Get all devices of a specific type."""
        return [device for device in self.devices.values() 
                if device.device_type == device_type and device.is_active]
                
    def get_all_devices(self) -> List[Device]:
        """Get all active devices."""
        return [device for device in self.devices.values() if device.is_active]

    def add_product(self, category: str, product: Dict):
        """Add a product to a category."""
        if category in self.products:
            self.products[category].append(product)
            self.save_data()

    def remove_product(self, category: str, product: Dict):
        """Remove a product from a category."""
        if category in self.products and product in self.products[category]:
            self.products[category].remove(product)
            self.save_data()

    def get_products(self, category: str) -> List[Dict]:
        """Get all products in a category."""
        return self.products.get(category, [])

    def get_all_products(self) -> Dict[str, List[Dict]]:
        """Get all products by category."""
        return self.products.copy()
        
    def save_data(self):
        """Save all data to file."""
        data = {
            'devices': {device_id: device.to_dict() for device_id, device in self.devices.items()},
            'products': self.products,
            'last_saved': datetime.now().isoformat()
        }
        
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving data: {e}")
            
    def load_data(self):
        """Load data from file."""
        if not os.path.exists(self.data_file):
            return
            
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if 'devices' in data:
                self.devices = {
                    device_id: Device.from_dict(device_data)
                    for device_id, device_data in data['devices'].items()
                }

            if 'products' in data:
                self.products = data['products']
        except Exception as e:
            print(f"Error loading data: {e}")
            
    def get_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive statistics for the specified number of days."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        stats = {
            'total_revenue': 0.0,
            'total_sessions': 0,
            'total_active_time': timedelta(),
            'revenue_by_type': defaultdict(float),
            'sessions_by_type': defaultdict(int),
            'active_time_by_type': defaultdict(lambda: timedelta()),
            'revenue_by_device': {},
            'sessions_by_device': {},
            'active_time_by_device': {},
            'daily_revenue': defaultdict(float),
            'hourly_usage': defaultdict(int),
            'average_session_duration': {},
            'games_played': defaultdict(int)  # For babyfoot
        }
        
        for device in self.devices.values():
            if not device.is_active:
                continue
                
            device_revenue = 0.0
            device_sessions = 0
            device_active_time = timedelta()
            device_games = 0
            
            # Process session history
            for session in device.session_history:
                if session.start_time >= cutoff_date:
                    device_revenue += session.total_cost
                    device_sessions += 1
                    device_active_time += session.get_duration()
                    device_games += session.games_played
                    
                    # Daily revenue
                    day_key = session.start_time.strftime('%Y-%m-%d')
                    stats['daily_revenue'][day_key] += session.total_cost
                    
                    # Hourly usage
                    hour_key = session.start_time.hour
                    stats['hourly_usage'][hour_key] += 1
                    
            # Process current session if exists
            if device.current_session and device.current_session.start_time >= cutoff_date:
                current_cost = device.current_session.get_current_cost()
                device_revenue += current_cost
                device_sessions += 1
                device_active_time += device.current_session.get_duration()
                device_games += device.current_session.games_played
                
                # Daily revenue for current session
                day_key = device.current_session.start_time.strftime('%Y-%m-%d')
                stats['daily_revenue'][day_key] += current_cost
                
                # Hourly usage for current session
                hour_key = device.current_session.start_time.hour
                stats['hourly_usage'][hour_key] += 1
                
            # Update totals
            stats['total_revenue'] += device_revenue
            stats['total_sessions'] += device_sessions
            stats['total_active_time'] += device_active_time
            
            # Update by type
            device_type_name = device.device_type.value
            stats['revenue_by_type'][device_type_name] += device_revenue
            stats['sessions_by_type'][device_type_name] += device_sessions
            stats['active_time_by_type'][device_type_name] += device_active_time
            stats['games_played'][device_type_name] += device_games
            
            # Update by device
            stats['revenue_by_device'][device.name] = device_revenue
            stats['sessions_by_device'][device.name] = device_sessions
            stats['active_time_by_device'][device.name] = device_active_time
            
            # Calculate average session duration
            if device_sessions > 0:
                avg_duration = device_active_time / device_sessions
                stats['average_session_duration'][device.name] = avg_duration
                
        # Convert defaultdicts to regular dicts for JSON serialization
        stats['revenue_by_type'] = dict(stats['revenue_by_type'])
        stats['sessions_by_type'] = dict(stats['sessions_by_type'])
        stats['active_time_by_type'] = {k: v.total_seconds() for k, v in stats['active_time_by_type'].items()}
        stats['daily_revenue'] = dict(stats['daily_revenue'])
        stats['hourly_usage'] = dict(stats['hourly_usage'])
        stats['games_played'] = dict(stats['games_played'])
        stats['total_active_time'] = stats['total_active_time'].total_seconds()
        stats['average_session_duration'] = {k: v.total_seconds() for k, v in stats['average_session_duration'].items()}
        
        return stats
        
    def get_device_history(self, device_id: str, days: int = 30) -> List[Session]:
        """Get session history for a specific device."""
        device = self.get_device(device_id)
        if not device:
            return []
            
        cutoff_date = datetime.now() - timedelta(days=days)
        history = [session for session in device.session_history 
                  if session.start_time >= cutoff_date]
                  
        # Add current session if exists
        if device.current_session and device.current_session.start_time >= cutoff_date:
            history.append(device.current_session)
            
        return sorted(history, key=lambda s: s.start_time, reverse=True)
        
    def backup_data(self, backup_file: Optional[str] = None):
        """Create a backup of the current data."""
        if backup_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"gameshop_backup_{timestamp}.json"
            
        try:
            import shutil
            shutil.copy2(self.data_file, backup_file)
            return backup_file
        except Exception as e:
            print(f"Error creating backup: {e}")
            return None
