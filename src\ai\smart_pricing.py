"""
Smart Pricing Module using PuLP for optimization
"""

import pulp
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import numpy as np


class SmartPricingEngine:
    """Advanced pricing optimization using linear programming."""
    
    def __init__(self):
        self.base_prices = {
            'ps5_1v1': 150.0,
            'ps5_2v2': 200.0,
            'arcade': 100.0,
            'pool': 120.0,
            'babyfoot': 80.0,
            'other': 90.0
        }
        self.demand_elasticity = {
            'ps5_1v1': -0.8,  # High elasticity - price sensitive
            'ps5_2v2': -0.6,  # Medium elasticity
            'arcade': -1.2,   # Very price sensitive
            'pool': -0.7,     # Medium elasticity
            'babyfoot': -0.9, # High elasticity
            'other': -1.0     # High elasticity
        }
        
    def optimize_prices(self, historical_data: Dict, constraints: Dict = None) -> Dict[str, float]:
        """
        Optimize prices using context-aware business logic.

        Args:
            historical_data: Dictionary with session data
            constraints: Optional pricing constraints

        Returns:
            Dictionary with optimized prices for each device type
        """
        if not historical_data or not historical_data.get('sessions_by_type'):
            return self.base_prices.copy()

        # Analyze business context
        total_revenue = historical_data.get('total_revenue', 0)
        total_sessions = historical_data.get('total_sessions', 0)
        sessions_by_type = historical_data.get('sessions_by_type', {})

        # Determine business scenario
        business_scenario = self._analyze_business_scenario(total_revenue, total_sessions, sessions_by_type)

        # Apply scenario-specific pricing strategy
        optimized_prices = self._apply_pricing_strategy(business_scenario, sessions_by_type, total_sessions)

        return optimized_prices

    def _analyze_business_scenario(self, total_revenue: float, total_sessions: int, sessions_by_type: Dict) -> str:
        """Analyze current business scenario to determine pricing strategy."""

        avg_session_value = total_revenue / max(total_sessions, 1)

        # Scenario classification
        if total_revenue > 2000 and total_sessions > 30:
            return 'excellent'  # High demand, high revenue
        elif total_revenue > 1000 and total_sessions > 15:
            return 'good'       # Stable business
        elif total_revenue > 500 and total_sessions > 8:
            return 'fair'       # Moderate performance
        elif total_sessions < 5:
            return 'slow'       # Low activity - need to boost demand
        elif avg_session_value < 50:
            return 'low_value'  # Low spending per session
        else:
            return 'needs_attention'  # General underperformance

    def _apply_pricing_strategy(self, scenario: str, sessions_by_type: Dict, total_sessions: int) -> Dict[str, float]:
        """Apply pricing strategy based on business scenario."""

        optimized_prices = self.base_prices.copy()

        if scenario == 'excellent':
            # High demand - can increase prices moderately
            for device in optimized_prices:
                # 5-15% increase for popular devices
                device_sessions = sessions_by_type.get(device, 0)
                popularity = device_sessions / max(total_sessions, 1)

                if popularity > 0.2:  # Popular device (>20% usage)
                    increase = 0.10 + (popularity * 0.1)  # 10-20% increase
                    optimized_prices[device] *= (1 + min(increase, 0.15))
                else:
                    # Small increase for less popular devices
                    optimized_prices[device] *= 1.05

        elif scenario == 'good':
            # Stable - minor optimizations only
            for device in optimized_prices:
                device_sessions = sessions_by_type.get(device, 0)
                popularity = device_sessions / max(total_sessions, 1)

                if popularity > 0.25:  # Very popular
                    optimized_prices[device] *= 1.08  # 8% increase
                elif popularity < 0.1:  # Underused
                    optimized_prices[device] *= 0.95  # 5% decrease
                # else keep same price

        elif scenario == 'fair':
            # Moderate performance - focus on underperforming devices
            for device in optimized_prices:
                device_sessions = sessions_by_type.get(device, 0)
                popularity = device_sessions / max(total_sessions, 1)

                if popularity < 0.15:  # Underused devices
                    optimized_prices[device] *= 0.90  # 10% decrease to boost demand
                elif popularity > 0.3:  # Popular devices
                    optimized_prices[device] *= 1.05  # Small 5% increase

        elif scenario == 'slow':
            # Low activity - aggressive discounts to boost demand
            for device in optimized_prices:
                # 10-20% discounts across the board
                discount = 0.15 + (0.05 * (1 - sessions_by_type.get(device, 0) / max(total_sessions, 1)))
                optimized_prices[device] *= (1 - min(discount, 0.25))

        elif scenario == 'low_value':
            # Low spending per session - reduce prices to increase volume
            for device in optimized_prices:
                optimized_prices[device] *= 0.85  # 15% reduction

        elif scenario == 'needs_attention':
            # General underperformance - mixed strategy
            for device in optimized_prices:
                device_sessions = sessions_by_type.get(device, 0)
                if device_sessions == 0:
                    optimized_prices[device] *= 0.80  # 20% discount for unused
                else:
                    optimized_prices[device] *= 0.92  # 8% discount for others

        # Ensure PS5 2v2 is always more expensive than 1v1
        if optimized_prices['ps5_2v2'] <= optimized_prices['ps5_1v1']:
            optimized_prices['ps5_2v2'] = optimized_prices['ps5_1v1'] * 1.25

        # Round to reasonable values
        for device in optimized_prices:
            optimized_prices[device] = round(optimized_prices[device], 2)

        return optimized_prices

    def calculate_demand_forecast(self, prices: Dict[str, float],
                                historical_data: Dict) -> Dict[str, int]:
        """
        Forecast demand based on optimized prices.
        
        Args:
            prices: Dictionary of prices for each device
            historical_data: Historical session data
            
        Returns:
            Dictionary with forecasted demand for each device
        """
        sessions_by_type = historical_data.get('sessions_by_type', {})
        forecasted_demand = {}
        
        for device, price in prices.items():
            base_sessions = sessions_by_type.get(device, 0)
            base_price = self.base_prices[device]
            elasticity = self.demand_elasticity[device]
            
            if base_sessions > 0 and base_price > 0:
                # Demand = base_demand * (base_price / current_price) ^ elasticity
                price_ratio = base_price / price
                demand_multiplier = price_ratio ** abs(elasticity)
                forecasted_demand[device] = max(1, int(base_sessions * demand_multiplier))
            else:
                forecasted_demand[device] = base_sessions
                
        return forecasted_demand
    
    def calculate_profit_prediction(self, prices: Dict[str, float], 
                                  demand: Dict[str, int],
                                  costs: Dict[str, float] = None) -> Dict:
        """
        Calculate profit predictions based on prices and demand.
        
        Args:
            prices: Optimized prices
            demand: Forecasted demand
            costs: Operating costs per device (optional)
            
        Returns:
            Dictionary with profit analysis
        """
        if costs is None:
            # Default operating costs (electricity, maintenance, etc.)
            costs = {device: price * 0.2 for device, price in prices.items()}
        
        profit_analysis = {
            'device_profits': {},
            'total_revenue': 0,
            'total_costs': 0,
            'total_profit': 0,
            'profit_margin': 0
        }
        
        for device in prices.keys():
            device_price = prices[device]
            device_demand = demand.get(device, 0)
            device_cost = costs.get(device, device_price * 0.2)
            
            revenue = device_price * device_demand
            cost = device_cost * device_demand
            profit = revenue - cost
            
            profit_analysis['device_profits'][device] = {
                'revenue': revenue,
                'cost': cost,
                'profit': profit,
                'margin': (profit / revenue * 100) if revenue > 0 else 0
            }
            
            profit_analysis['total_revenue'] += revenue
            profit_analysis['total_costs'] += cost
        
        profit_analysis['total_profit'] = (profit_analysis['total_revenue'] - 
                                         profit_analysis['total_costs'])
        
        if profit_analysis['total_revenue'] > 0:
            profit_analysis['profit_margin'] = (profit_analysis['total_profit'] / 
                                               profit_analysis['total_revenue'] * 100)
        
        return profit_analysis
    
    def get_pricing_recommendations(self, historical_data: Dict, 
                                  current_prices: Dict = None) -> Dict:
        """
        Get comprehensive pricing recommendations.
        
        Args:
            historical_data: Historical business data
            current_prices: Current pricing (optional)
            
        Returns:
            Dictionary with pricing recommendations and analysis
        """
        if current_prices is None:
            current_prices = self.base_prices.copy()
        
        # Optimize prices
        optimized_prices = self.optimize_prices(historical_data)
        
        # Calculate demand forecast
        demand_forecast = self.calculate_demand_forecast(optimized_prices, historical_data)
        
        # Calculate profit predictions
        profit_prediction = self.calculate_profit_prediction(optimized_prices, demand_forecast)
        
        # Generate recommendations
        recommendations = {
            'optimized_prices': optimized_prices,
            'current_prices': current_prices,
            'price_changes': {},
            'demand_forecast': demand_forecast,
            'profit_prediction': profit_prediction,
            'recommendations': []
        }
        
        # Calculate price changes
        for device in optimized_prices.keys():
            current = current_prices.get(device, self.base_prices[device])
            optimized = optimized_prices[device]
            change_pct = ((optimized - current) / current) * 100
            
            recommendations['price_changes'][device] = {
                'current': current,
                'optimized': optimized,
                'change_amount': optimized - current,
                'change_percent': change_pct
            }
            
            # Generate specific recommendations
            if abs(change_pct) > 5:  # Significant change
                if change_pct > 0:
                    recommendations['recommendations'].append(
                        f"📈 Increase {device.replace('_', ' ').title()} price by "
                        f"{change_pct:.1f}% to {optimized:.2f} DZD for better profitability"
                    )
                else:
                    recommendations['recommendations'].append(
                        f"📉 Decrease {device.replace('_', ' ').title()} price by "
                        f"{abs(change_pct):.1f}% to {optimized:.2f} DZD to boost demand"
                    )
        
        return recommendations
