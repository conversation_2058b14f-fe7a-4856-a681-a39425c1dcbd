# GameShop Manager

A comprehensive management system for gaming stations built with PySide2. This application helps you manage PS5 stations, arcade machines, babyfoot tables, and other gaming devices with session tracking, pricing management, and detailed analytics.

## Features

### 🎮 Multi-Panel Management
- **PS5 Panel**: Manage PS5 gaming stations with VIP and Regular tiers
- **Arcade Panel**: Control arcade machines with hourly pricing
- **Babyfoot Panel**: Handle foosball tables with game-based pricing
- **Other Panel**: Manage custom gaming equipment (VR stations, pool tables, etc.)

### 📊 Comprehensive Analytics
- Real-time statistics dashboard
- Revenue tracking by category and device
- Usage patterns and time analysis
- Session history with filtering and export
- Comparative performance metrics

### ⏱️ Session Management
- Start, pause, resume, and end sessions
- Real-time timer and cost calculation
- Automatic data persistence
- Session history tracking

### 💰 Flexible Pricing
- Hourly pricing for PS5, Arcade, and Other devices
- Game-based pricing for Babyfoot tables
- Editable pricing per device
- Revenue calculation and reporting

## Installation

### Prerequisites
- Python 3.7 or higher
- PySide2

### Setup
1. Clone or download the project files
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   python main.py
   ```

## Usage

### Getting Started
1. Launch the application using `python main.py`
2. Navigate between panels using the sidebar buttons
3. Create your first device by clicking "Create Station/Machine/Table/Device"
4. Start managing sessions and tracking revenue!

### Creating Devices
- **PS5 Stations**: Choose between Regular (300 DZD/hour) or VIP (500 DZD/hour)
- **Arcade Machines**: Set custom hourly rates (default: 200 DZD/hour)
- **Babyfoot Tables**: Set price per game (default: 50 DZD/game)
- **Other Devices**: Custom hourly rates for any gaming equipment

### Managing Sessions
1. **Start Session**: Click "Start Session" on any idle device
2. **Pause/Resume**: Use "Pause" button to temporarily stop the timer
3. **End Session**: Click "End Session" to complete and calculate final cost
4. **Sell Coin** (Babyfoot only): Add games to the current session

### Viewing Statistics
- Click the "📊 Statistics" button in the top header
- View comprehensive analytics across multiple tabs:
  - **Overview**: Key metrics and performance summary
  - **By Category**: Compare different device types
  - **By Device**: Individual device performance
  - **Time Analysis**: Daily and hourly usage patterns

### Session History
- Click "📋 History" in any panel to view session records
- Filter by device, date range, and session status
- Export data to CSV for external analysis

## File Structure

```
gameshop/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── src/
│   ├── main_window.py     # Main application window
│   ├── models/            # Data models
│   │   ├── device.py      # Device and session classes
│   │   └── data_manager.py # Data persistence and statistics
│   ├── panels/            # UI panels
│   │   ├── ps5_panel.py   # PS5 management panel
│   │   ├── arcade_panel.py # Arcade management panel
│   │   ├── babyfoot_panel.py # Babyfoot management panel
│   │   ├── other_panel.py # Other devices panel
│   │   ├── statistics_panel.py # Analytics dashboard
│   │   └── history_panel.py # Session history viewer
│   └── widgets/           # Custom widgets
│       └── device_widget.py # Individual device control widget
└── gameshop_data.json     # Data storage file (created automatically)
```

## Data Storage

The application automatically saves all data to `gameshop_data.json` in the application directory. This includes:
- Device configurations
- Session history
- Pricing information
- All statistics data

Data is saved automatically when:
- Creating or deleting devices
- Starting or ending sessions
- Closing the application

## Currency

All pricing is displayed in DZD (Algerian Dinar). You can modify the currency by editing the suffix in the price input fields throughout the application.

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Ensure PySide2 is installed: `pip install PySide2`
   - Check Python version (3.7+ required)

2. **Data not saving**
   - Ensure write permissions in the application directory
   - Check for disk space

3. **Import errors**
   - Verify all files are in the correct directory structure
   - Run from the main application directory

### Testing

Run the test script to verify functionality:
```bash
python test_app.py
```

## Development

### Adding New Device Types
1. Add new enum value to `DeviceType` in `src/models/device.py`
2. Create corresponding panel in `src/panels/`
3. Add panel to main window navigation
4. Update statistics calculations if needed

### Customizing UI
- Modify styles in `src/main_window.py` `apply_styling()` method
- Adjust colors and fonts in individual panel files
- Update icons by changing emoji characters in button texts

## License

This project is provided as-is for educational and commercial use.

## Support

For issues or questions, please check the troubleshooting section above or review the code comments for implementation details.
